# 监控仪表板配置指南

## 📊 概述

本文档提供了 Master-Know 系统监控仪表板的配置指南，包括 Grafana 仪表板、Prometheus 指标收集和告警规则配置。

## 🏗️ 监控架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Prometheus    │───▶│     Grafana     │
│   (FastAPI)     │    │   (Metrics)     │    │   (Dashboard)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Structured    │    │   AlertManager  │    │   Notification  │
│     Logs        │    │    (Alerts)     │    │   (Slack/Email) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📈 关键指标

### 1. 应用性能指标

#### HTTP 请求指标
```yaml
# 请求总数
http_requests_total{method, endpoint, status_code}

# 请求持续时间
http_request_duration_seconds{method, endpoint}

# 请求大小
http_request_size_bytes{method, endpoint}

# 响应大小
http_response_size_bytes{method, endpoint}
```

#### 业务指标
```yaml
# 文档处理指标
documents_processed_total{status}
documents_processing_duration_seconds
documents_chunks_created_total

# 搜索指标
search_requests_total{search_type}
search_duration_seconds{search_type}
search_results_count{search_type}

# 对话指标
conversations_created_total
conversation_messages_total
conversation_duration_seconds
```

### 2. 系统资源指标

#### CPU 和内存
```yaml
# CPU 使用率
process_cpu_seconds_total
system_cpu_usage_percent

# 内存使用
process_resident_memory_bytes
system_memory_usage_percent
system_memory_available_bytes
```

#### 数据库指标
```yaml
# 连接池
database_connections_active
database_connections_idle
database_connections_total

# 查询性能
database_query_duration_seconds{operation}
database_queries_total{operation, status}
```

### 3. 错误和异常指标

```yaml
# 错误计数
errors_total{error_type, endpoint}
exceptions_total{exception_type}

# 错误率
error_rate_percent{endpoint}
```

## 📊 Grafana 仪表板配置

### 主仪表板 (Main Dashboard)

```json
{
  "dashboard": {
    "title": "Master-Know System Overview",
    "panels": [
      {
        "title": "Request Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status_code=~\"4..|5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "Error Rate %"
          }
        ]
      }
    ]
  }
}
```

### 应用性能仪表板

```json
{
  "dashboard": {
    "title": "Application Performance",
    "panels": [
      {
        "title": "Document Processing",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(documents_processed_total[5m])",
            "legendFormat": "Documents/sec"
          }
        ]
      },
      {
        "title": "Search Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(search_duration_seconds_bucket[5m]))",
            "legendFormat": "Search Response Time (95th)"
          }
        ]
      },
      {
        "title": "Database Queries",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(database_queries_total[5m])",
            "legendFormat": "Queries/sec"
          }
        ]
      }
    ]
  }
}
```

## 🚨 告警规则配置

### Prometheus 告警规则

```yaml
# alerts.yml
groups:
  - name: master-know-alerts
    rules:
      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # 响应时间过长告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}%"

      - alert: HighMemoryUsage
        expr: system_memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}%"

      # 数据库连接告警
      - alert: DatabaseConnectionsHigh
        expr: database_connections_active / database_connections_total > 0.8
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Database connections running high"
          description: "{{ $value | humanizePercentage }} of database connections are in use"

      # 服务不可用告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute"
```

### AlertManager 配置

```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
  - name: 'web.hook'
    email_configs:
      - to: '<EMAIL>'
        subject: 'Master-Know Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
    
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts'
        title: 'Master-Know Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          {{ end }}
```

## 🔧 部署配置

### Docker Compose 监控栈

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alerts.yml:/etc/prometheus/alerts.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:
```

### Prometheus 配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'master-know-api'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/api/v1/metrics'
    scrape_interval: 5s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
```

## 📱 监控最佳实践

### 1. 指标命名规范

```python
# 好的指标命名
http_requests_total
http_request_duration_seconds
database_connections_active

# 避免的命名
requests  # 太模糊
db_conn   # 缩写不清晰
time      # 没有单位
```

### 2. 标签使用

```python
# 合理使用标签
http_requests_total{method="GET", endpoint="/api/v1/documents", status_code="200"}

# 避免高基数标签
http_requests_total{user_id="12345"}  # 用户ID会产生大量时间序列
```

### 3. 告警设计原则

- **可操作性**: 每个告警都应该有明确的处理步骤
- **相关性**: 告警应该指示真正的问题
- **及时性**: 在问题影响用户之前发出告警
- **避免噪音**: 减少误报和重复告警

### 4. 仪表板设计

- **分层设计**: 从概览到详细的层次结构
- **用户导向**: 根据不同角色设计不同视图
- **实时性**: 关键指标应该实时更新
- **可读性**: 使用清晰的图表和颜色

## 🚀 快速开始

### 1. 启动监控栈

```bash
# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 检查服务状态
docker-compose -f docker-compose.monitoring.yml ps
```

### 2. 访问监控界面

- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093

### 3. 导入仪表板

1. 登录 Grafana
2. 导入预配置的仪表板
3. 配置数据源指向 Prometheus
4. 验证指标数据显示正常

### 4. 测试告警

```bash
# 模拟高负载
ab -n 1000 -c 10 http://localhost:8000/api/v1/health

# 检查告警是否触发
curl http://localhost:9093/api/v1/alerts
```

## 📞 故障排查

### 常见问题

1. **指标不显示**: 检查 Prometheus 目标状态
2. **告警不触发**: 验证告警规则语法
3. **仪表板空白**: 确认数据源配置正确
4. **通知不发送**: 检查 AlertManager 配置

### 调试命令

```bash
# 检查 Prometheus 目标
curl http://localhost:9090/api/v1/targets

# 查询指标
curl 'http://localhost:9090/api/v1/query?query=up'

# 检查告警规则
curl http://localhost:9090/api/v1/rules
```
