# 文档管理 API

## 📄 概述

文档管理 API 提供完整的文档生命周期管理功能，包括文档上传、处理、分割、索引和检索等核心功能。

## 🔗 基础路径

```
/api/v1/documents
```

## 📚 API 端点

### 1. 创建文档

创建新的文档记录。

```http
POST /api/v1/documents/
```

**请求体**:
```json
{
  "title": "我的学习笔记",
  "content": "这是文档的完整内容...",
  "file_type": "txt",
  "file_path": "/uploads/notes.txt",
  "description": "关于机器学习的学习笔记",
  "tags": ["机器学习", "笔记", "AI"]
}
```

**响应示例**:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "title": "我的学习笔记",
  "content": "这是文档的完整内容...",
  "file_type": "txt",
  "file_path": "/uploads/notes.txt",
  "description": "关于机器学习的学习笔记",
  "tags": ["机器学习", "笔记", "AI"],
  "status": "uploaded",
  "size": 1024,
  "owner_id": "user123",
  "created_at": "2025-08-16T14:30:00Z",
  "updated_at": "2025-08-16T14:30:00Z"
}
```

**错误响应**:
```json
{
  "detail": [
    {
      "type": "string_too_short",
      "loc": ["body", "title"],
      "msg": "String should have at least 1 character",
      "input": ""
    }
  ]
}
```

### 2. 获取文档列表

获取当前用户的文档列表，支持分页和过滤。

```http
GET /api/v1/documents/
```

**查询参数**:
- `skip` (int, 可选): 跳过的记录数，默认 0
- `limit` (int, 可选): 返回的记录数，默认 100，最大 1000
- `status` (string, 可选): 文档状态过滤 (`uploaded`, `processing`, `processed`, `failed`)
- `file_type` (string, 可选): 文件类型过滤 (`txt`, `md`, `pdf`, `docx`)
- `search` (string, 可选): 标题和描述搜索关键词

**请求示例**:
```http
GET /api/v1/documents/?skip=0&limit=20&status=processed&search=机器学习
```

**响应示例**:
```json
{
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "title": "我的学习笔记",
      "description": "关于机器学习的学习笔记",
      "file_type": "txt",
      "status": "processed",
      "size": 1024,
      "created_at": "2025-08-16T14:30:00Z",
      "chunk_count": 5
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 20
}
```

### 3. 获取单个文档

根据文档ID获取详细信息。

```http
GET /api/v1/documents/{document_id}
```

**路径参数**:
- `document_id` (UUID): 文档唯一标识符

**响应示例**:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "title": "我的学习笔记",
  "content": "这是文档的完整内容...",
  "file_type": "txt",
  "file_path": "/uploads/notes.txt",
  "description": "关于机器学习的学习笔记",
  "tags": ["机器学习", "笔记", "AI"],
  "status": "processed",
  "size": 1024,
  "owner_id": "user123",
  "created_at": "2025-08-16T14:30:00Z",
  "updated_at": "2025-08-16T14:30:00Z",
  "processing_stats": {
    "chunks_created": 5,
    "processing_time": 2.5,
    "strategy_used": "token_based"
  }
}
```

### 4. 更新文档

更新文档信息。

```http
PUT /api/v1/documents/{document_id}
```

**请求体**:
```json
{
  "title": "更新后的标题",
  "description": "更新后的描述",
  "tags": ["更新", "标签"]
}
```

**响应**: 返回更新后的完整文档信息

### 5. 删除文档

删除指定文档及其所有相关数据。

```http
DELETE /api/v1/documents/{document_id}
```

**响应**:
```json
{
  "message": "Document deleted successfully"
}
```

### 6. 处理文档

触发文档分割和索引处理。

```http
POST /api/v1/documents/{document_id}/process
```

**请求体** (可选):
```json
{
  "strategy": "token_based",
  "max_tokens": 500,
  "overlap": 50,
  "force_reprocess": false
}
```

**响应示例**:
```json
{
  "success": true,
  "document_id": "123e4567-e89b-12d3-a456-************",
  "document_title": "我的学习笔记",
  "chunks_created": 5,
  "strategy_used": "TokenBasedStrategy",
  "statistics": {
    "total_chunks": 5,
    "avg_chunk_size": 200,
    "min_chunk_size": 150,
    "max_chunk_size": 250,
    "total_tokens": 1000
  },
  "message": "Successfully processed document '我的学习笔记' into 5 chunks"
}
```

### 7. 重新处理文档

使用新的策略重新处理文档。

```http
POST /api/v1/documents/{document_id}/reprocess
```

**请求体**:
```json
{
  "strategy": "character_based",
  "max_chars": 1000,
  "preserve_headers": true
}
```

### 8. 获取文档分块

获取文档的所有分块信息。

```http
GET /api/v1/documents/{document_id}/chunks
```

**查询参数**:
- `skip` (int, 可选): 跳过的分块数
- `limit` (int, 可选): 返回的分块数

**响应示例**:
```json
{
  "data": [
    {
      "id": "chunk123",
      "document_id": "123e4567-e89b-12d3-a456-************",
      "content": "这是第一个文本块的内容...",
      "chunk_index": 0,
      "start_char": 0,
      "end_char": 200,
      "token_count": 45,
      "created_at": "2025-08-16T14:30:00Z"
    }
  ],
  "total": 5,
  "skip": 0,
  "limit": 100
}
```

### 9. 获取文档统计

获取文档处理统计信息。

```http
GET /api/v1/documents/{document_id}/stats
```

**响应示例**:
```json
{
  "document_id": "123e4567-e89b-12d3-a456-************",
  "title": "我的学习笔记",
  "status": "processed",
  "size": 1024,
  "word_count": 200,
  "char_count": 1024,
  "chunk_stats": {
    "total_chunks": 5,
    "avg_chunk_size": 200,
    "min_chunk_size": 150,
    "max_chunk_size": 250,
    "total_tokens": 1000,
    "avg_tokens_per_chunk": 200
  },
  "processing_info": {
    "strategy_used": "token_based",
    "processing_time": 2.5,
    "processed_at": "2025-08-16T14:30:00Z"
  }
}
```

## 📊 数据模型

### Document 模型

```typescript
interface Document {
  id: string;                    // UUID
  title: string;                 // 文档标题
  content: string;               // 文档内容
  file_type: string;             // 文件类型 (txt, md, pdf, docx)
  file_path?: string;            // 文件路径
  description?: string;          // 文档描述
  tags: string[];                // 标签列表
  status: DocumentStatus;        // 文档状态
  size: number;                  // 文件大小(字节)
  owner_id: string;              // 所有者ID
  created_at: string;            // 创建时间 (ISO 8601)
  updated_at: string;            // 更新时间 (ISO 8601)
}

type DocumentStatus = 'uploaded' | 'processing' | 'processed' | 'failed';
```

### DocumentChunk 模型

```typescript
interface DocumentChunk {
  id: string;                    // 分块ID
  document_id: string;           // 所属文档ID
  content: string;               // 分块内容
  chunk_index: number;           // 分块索引
  start_char: number;            // 起始字符位置
  end_char: number;              // 结束字符位置
  token_count: number;           // Token数量
  embedding?: number[];          // 向量嵌入
  created_at: string;            // 创建时间
}
```

## ⚠️ 错误码说明

| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| `DOCUMENT_NOT_FOUND` | 404 | 文档不存在 | 检查文档ID是否正确 |
| `DOCUMENT_PROCESSING` | 409 | 文档正在处理中 | 等待处理完成后重试 |
| `INVALID_FILE_TYPE` | 400 | 不支持的文件类型 | 使用支持的文件类型 |
| `FILE_TOO_LARGE` | 413 | 文件过大 | 减小文件大小或分割文件 |
| `PROCESSING_FAILED` | 500 | 文档处理失败 | 检查文档内容或联系支持 |

## 💡 使用示例

### Python 示例

```python
import requests

# 配置
BASE_URL = "http://localhost:8000/api/v1"
headers = {"Authorization": "Bearer YOUR_TOKEN"}

# 创建文档
doc_data = {
    "title": "Python学习笔记",
    "content": "Python是一种高级编程语言...",
    "file_type": "txt",
    "tags": ["Python", "编程", "学习"]
}

response = requests.post(f"{BASE_URL}/documents/", json=doc_data, headers=headers)
document = response.json()
document_id = document["id"]

# 处理文档
process_response = requests.post(
    f"{BASE_URL}/documents/{document_id}/process",
    headers=headers
)
print(f"处理结果: {process_response.json()}")

# 获取分块
chunks_response = requests.get(
    f"{BASE_URL}/documents/{document_id}/chunks",
    headers=headers
)
chunks = chunks_response.json()["data"]
print(f"文档被分割为 {len(chunks)} 个分块")
```

### JavaScript 示例

```javascript
const API_BASE = 'http://localhost:8000/api/v1';
const token = 'YOUR_TOKEN';

// 创建文档
async function createDocument() {
  const response = await fetch(`${API_BASE}/documents/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      title: 'JavaScript学习笔记',
      content: 'JavaScript是一种动态编程语言...',
      file_type: 'txt',
      tags: ['JavaScript', '前端', '学习']
    })
  });
  
  const document = await response.json();
  return document.id;
}

// 处理文档
async function processDocument(documentId) {
  const response = await fetch(`${API_BASE}/documents/${documentId}/process`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
}
```
