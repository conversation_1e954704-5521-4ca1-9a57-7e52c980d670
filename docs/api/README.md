# Master-Know API 文档

## 📖 概述

Master-Know 提供完整的 RESTful API，支持智能学习导师系统的所有核心功能，包括文档管理、智能搜索、对话交互、主题管理等。

## 🚀 快速开始

### 基础信息

- **Base URL**: `http://localhost:8000/api/v1`
- **API 版本**: v1.0.0
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 在线文档

- **Swagger UI**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)
- **OpenAPI Schema**: [http://localhost:8000/api/v1/openapi.json](http://localhost:8000/api/v1/openapi.json)

## 🔐 认证授权

### 获取访问令牌

```http
POST /api/v1/login/access-token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=changethis
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 11520
}
```

### 使用访问令牌

在所有需要认证的请求中添加 Authorization 头：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📚 API 模块

### 核心模块

| 模块 | 路径前缀 | 描述 | 文档链接 |
|------|----------|------|----------|
| 🔐 认证 | `/login` | 用户认证和令牌管理 | [认证API](./auth.md) |
| 👤 用户 | `/users` | 用户管理和个人资料 | [用户API](./users.md) |
| 📄 文档 | `/documents` | 文档上传、处理和管理 | [文档API](./documents.md) |
| 🔍 搜索 | `/search` | 智能搜索和检索 | [搜索API](./search.md) |
| 💬 对话 | `/conversations` | AI对话和交互 | [对话API](./conversations.md) |
| 📚 主题 | `/topics` | 主题管理和知识组织 | [主题API](./topics.md) |
| 📝 摘要 | `/summaries` | 内容摘要和总结 | [摘要API](./summaries.md) |

### 扩展模块

| 模块 | 路径前缀 | 描述 | 文档链接 |
|------|----------|------|----------|
| 🔗 嵌入 | `/embedding` | 向量嵌入服务 | [嵌入API](./embedding.md) |
| 🤖 LLM | `/llm` | 大语言模型集成 | [LLM API](./llm.md) |
| 📊 监控 | `/monitoring` | 系统监控和统计 | [监控API](./monitoring.md) |
| 🧪 集成测试 | `/integration` | 开发环境测试接口 | [测试API](./integration.md) |

## 🏗️ 数据模型

### 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-08-16T14:30:00Z"
}
```

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "title",
      "issue": "标题不能为空"
    }
  },
  "timestamp": "2025-08-16T14:30:00Z"
}
```

## 📋 状态码说明

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 204 | No Content | 请求成功，无返回内容 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或令牌无效 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 422 | Unprocessable Entity | 请求格式正确但语义错误 |
| 429 | Too Many Requests | 请求频率超限 |
| 500 | Internal Server Error | 服务器内部错误 |

## 🔧 开发工具

### 客户端生成

使用 OpenAPI 规范自动生成客户端代码：

```bash
# 生成 TypeScript 客户端
npm run generate-client

# 生成 Python 客户端
openapi-generator-cli generate -i openapi.json -g python -o ./python-client
```

### API 测试

```bash
# 使用 curl 测试
curl -X GET "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 使用 httpie 测试
http GET localhost:8000/api/v1/users/me Authorization:"Bearer YOUR_TOKEN"
```

## 📖 使用示例

### 完整工作流程示例

```python
import requests

# 1. 获取访问令牌
auth_response = requests.post(
    "http://localhost:8000/api/v1/login/access-token",
    data={"username": "<EMAIL>", "password": "changethis"}
)
token = auth_response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# 2. 上传文档
doc_data = {
    "title": "测试文档",
    "content": "这是一个测试文档的内容...",
    "file_type": "txt"
}
doc_response = requests.post(
    "http://localhost:8000/api/v1/documents/",
    json=doc_data,
    headers=headers
)
document_id = doc_response.json()["id"]

# 3. 处理文档
process_response = requests.post(
    f"http://localhost:8000/api/v1/documents/{document_id}/process",
    headers=headers
)

# 4. 搜索文档
search_response = requests.get(
    "http://localhost:8000/api/v1/search/",
    params={"q": "测试", "limit": 10},
    headers=headers
)

# 5. 创建对话
conversation_data = {"title": "关于测试文档的讨论"}
conv_response = requests.post(
    "http://localhost:8000/api/v1/conversations/",
    json=conversation_data,
    headers=headers
)
```

## 🚨 最佳实践

### 1. 错误处理
- 始终检查响应状态码
- 解析错误响应中的详细信息
- 实现适当的重试机制

### 2. 性能优化
- 使用分页参数控制返回数据量
- 利用缓存机制减少重复请求
- 合理设置请求超时时间

### 3. 安全考虑
- 妥善保管访问令牌
- 定期刷新令牌
- 使用 HTTPS 进行生产环境通信

## 📞 支持与反馈

- **问题报告**: [GitHub Issues](https://github.com/your-org/master-know/issues)
- **功能请求**: [GitHub Discussions](https://github.com/your-org/master-know/discussions)
- **技术支持**: <EMAIL>

## 📄 许可证

本 API 文档遵循 [MIT License](../LICENSE) 许可证。
