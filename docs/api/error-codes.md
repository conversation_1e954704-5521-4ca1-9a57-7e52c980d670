# API 错误码和处理指南

## 📋 概述

本文档详细说明了 Master-Know API 的错误码体系、错误处理机制和最佳实践，帮助开发者正确处理各种异常情况。

## 🏗️ 错误响应结构

### 标准错误响应格式

```json
{
  "detail": {
    "error_code": "DOCUMENT_NOT_FOUND",
    "message": "指定的文档不存在",
    "details": {
      "document_id": "123e4567-e89b-12d3-a456-426614174000",
      "requested_at": "2025-08-16T14:30:00Z"
    },
    "suggestion": "请检查文档ID是否正确，或确认您有访问该文档的权限"
  },
  "status_code": 404,
  "timestamp": "2025-08-16T14:30:00Z",
  "path": "/api/v1/documents/123e4567-e89b-12d3-a456-426614174000"
}
```

### 验证错误响应格式

```json
{
  "detail": [
    {
      "type": "string_too_short",
      "loc": ["body", "title"],
      "msg": "String should have at least 1 character",
      "input": "",
      "ctx": {
        "min_length": 1
      }
    },
    {
      "type": "value_error",
      "loc": ["body", "file_type"],
      "msg": "Unsupported file type",
      "input": "xyz"
    }
  ]
}
```

## 🔢 HTTP 状态码分类

### 2xx 成功状态码

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | OK | 请求成功，返回数据 |
| 201 | Created | 资源创建成功 |
| 202 | Accepted | 请求已接受，异步处理中 |
| 204 | No Content | 请求成功，无返回内容 |

### 4xx 客户端错误状态码

| 状态码 | 含义 | 常见原因 | 解决方案 |
|--------|------|----------|----------|
| 400 | Bad Request | 请求参数错误 | 检查请求格式和参数 |
| 401 | Unauthorized | 未认证或令牌无效 | 重新登录获取有效令牌 |
| 403 | Forbidden | 权限不足 | 检查用户权限或联系管理员 |
| 404 | Not Found | 资源不存在 | 确认资源ID或路径正确 |
| 409 | Conflict | 资源冲突 | 检查资源状态或重试 |
| 413 | Payload Too Large | 请求体过大 | 减小请求数据大小 |
| 422 | Unprocessable Entity | 语义错误 | 修正请求数据格式 |
| 429 | Too Many Requests | 请求频率超限 | 降低请求频率或等待 |

### 5xx 服务器错误状态码

| 状态码 | 含义 | 常见原因 | 解决方案 |
|--------|------|----------|----------|
| 500 | Internal Server Error | 服务器内部错误 | 联系技术支持 |
| 502 | Bad Gateway | 网关错误 | 检查服务状态或重试 |
| 503 | Service Unavailable | 服务不可用 | 等待服务恢复 |
| 504 | Gateway Timeout | 网关超时 | 增加超时时间或重试 |

## 🏷️ 业务错误码

### 认证和授权错误 (AUTH_*)

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `AUTH_TOKEN_MISSING` | 401 | 缺少认证令牌 | 在请求头中添加 Authorization |
| `AUTH_TOKEN_INVALID` | 401 | 令牌无效或已过期 | 重新登录获取新令牌 |
| `AUTH_TOKEN_EXPIRED` | 401 | 令牌已过期 | 使用刷新令牌或重新登录 |
| `AUTH_INSUFFICIENT_PERMISSIONS` | 403 | 权限不足 | 联系管理员获取相应权限 |
| `AUTH_ACCOUNT_DISABLED` | 403 | 账户已禁用 | 联系管理员激活账户 |

### 用户管理错误 (USER_*)

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `USER_NOT_FOUND` | 404 | 用户不存在 | 检查用户ID是否正确 |
| `USER_EMAIL_EXISTS` | 409 | 邮箱已存在 | 使用不同的邮箱地址 |
| `USER_INVALID_CREDENTIALS` | 400 | 登录凭据无效 | 检查用户名和密码 |
| `USER_PROFILE_INCOMPLETE` | 422 | 用户资料不完整 | 完善必填的用户信息 |

### 文档管理错误 (DOCUMENT_*)

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `DOCUMENT_NOT_FOUND` | 404 | 文档不存在 | 检查文档ID是否正确 |
| `DOCUMENT_ACCESS_DENIED` | 403 | 无权访问文档 | 确认文档访问权限 |
| `DOCUMENT_PROCESSING` | 409 | 文档正在处理中 | 等待处理完成后重试 |
| `DOCUMENT_PROCESSING_FAILED` | 500 | 文档处理失败 | 检查文档格式或重新上传 |
| `DOCUMENT_TOO_LARGE` | 413 | 文档过大 | 减小文档大小或分割文档 |
| `DOCUMENT_INVALID_FORMAT` | 400 | 文档格式不支持 | 使用支持的文档格式 |
| `DOCUMENT_EMPTY_CONTENT` | 400 | 文档内容为空 | 确保文档包含有效内容 |

### 搜索相关错误 (SEARCH_*)

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `SEARCH_QUERY_EMPTY` | 400 | 搜索查询为空 | 提供有效的搜索关键词 |
| `SEARCH_QUERY_TOO_LONG` | 400 | 搜索查询过长 | 缩短搜索查询长度 |
| `SEARCH_INDEX_UNAVAILABLE` | 503 | 搜索索引不可用 | 等待索引服务恢复 |
| `SEARCH_TIMEOUT` | 504 | 搜索超时 | 简化查询或稍后重试 |
| `SEARCH_INVALID_FILTER` | 400 | 搜索过滤器无效 | 检查过滤器格式和值 |

### 对话管理错误 (CONVERSATION_*)

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `CONVERSATION_NOT_FOUND` | 404 | 对话不存在 | 检查对话ID是否正确 |
| `CONVERSATION_ENDED` | 409 | 对话已结束 | 创建新对话或重新激活 |
| `CONVERSATION_MESSAGE_TOO_LONG` | 400 | 消息过长 | 缩短消息长度 |
| `CONVERSATION_RATE_LIMITED` | 429 | 对话频率超限 | 降低消息发送频率 |

### 系统级错误 (SYSTEM_*)

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `SYSTEM_MAINTENANCE` | 503 | 系统维护中 | 等待维护完成 |
| `SYSTEM_OVERLOADED` | 503 | 系统负载过高 | 稍后重试 |
| `SYSTEM_DATABASE_ERROR` | 500 | 数据库错误 | 联系技术支持 |
| `SYSTEM_EXTERNAL_SERVICE_ERROR` | 502 | 外部服务错误 | 检查外部服务状态 |

## 🛠️ 错误处理最佳实践

### 1. 客户端错误处理

```python
import requests
from typing import Optional, Dict, Any

class APIError(Exception):
    def __init__(self, error_code: str, message: str, status_code: int, details: Optional[Dict] = None):
        self.error_code = error_code
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(f"{error_code}: {message}")

class MasterKnowClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """统一处理API响应和错误"""
        if response.status_code < 400:
            return response.json()
        
        try:
            error_data = response.json()
            if isinstance(error_data.get('detail'), dict):
                # 业务错误
                detail = error_data['detail']
                raise APIError(
                    error_code=detail.get('error_code', 'UNKNOWN_ERROR'),
                    message=detail.get('message', 'Unknown error occurred'),
                    status_code=response.status_code,
                    details=detail.get('details', {})
                )
            else:
                # 验证错误
                raise APIError(
                    error_code='VALIDATION_ERROR',
                    message='Request validation failed',
                    status_code=response.status_code,
                    details={'validation_errors': error_data.get('detail', [])}
                )
        except ValueError:
            # 无法解析JSON响应
            raise APIError(
                error_code='RESPONSE_PARSE_ERROR',
                message=f'HTTP {response.status_code}: {response.text}',
                status_code=response.status_code
            )
    
    def create_document(self, title: str, content: str, **kwargs) -> Dict[str, Any]:
        """创建文档，包含错误处理"""
        try:
            response = requests.post(
                f"{self.base_url}/documents/",
                json={"title": title, "content": content, **kwargs},
                headers=self.headers,
                timeout=30
            )
            return self._handle_response(response)
        
        except APIError as e:
            if e.error_code == 'DOCUMENT_TOO_LARGE':
                print(f"文档过大，请减小文档大小: {e.message}")
                # 可以尝试分割文档
                return self._split_and_create_document(title, content, **kwargs)
            elif e.error_code == 'AUTH_TOKEN_EXPIRED':
                print("令牌已过期，请重新登录")
                # 可以尝试刷新令牌
                raise
            else:
                print(f"创建文档失败: {e}")
                raise
        
        except requests.exceptions.Timeout:
            raise APIError('REQUEST_TIMEOUT', '请求超时', 408)
        except requests.exceptions.ConnectionError:
            raise APIError('CONNECTION_ERROR', '连接失败', 503)
```

### 2. JavaScript 错误处理

```javascript
class APIClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        await this.handleError(response);
      }
      
      return await response.json();
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      
      // 网络错误或其他异常
      throw new APIError('NETWORK_ERROR', error.message, 0);
    }
  }

  async handleError(response) {
    let errorData;
    try {
      errorData = await response.json();
    } catch {
      throw new APIError(
        'RESPONSE_PARSE_ERROR',
        `HTTP ${response.status}: ${response.statusText}`,
        response.status
      );
    }

    if (errorData.detail && typeof errorData.detail === 'object' && errorData.detail.error_code) {
      // 业务错误
      const { error_code, message, details } = errorData.detail;
      throw new APIError(error_code, message, response.status, details);
    } else {
      // 验证错误
      throw new APIError(
        'VALIDATION_ERROR',
        'Request validation failed',
        response.status,
        { validation_errors: errorData.detail }
      );
    }
  }

  async createDocument(documentData) {
    try {
      return await this.request('/documents/', {
        method: 'POST',
        body: JSON.stringify(documentData)
      });
    } catch (error) {
      if (error.errorCode === 'DOCUMENT_TOO_LARGE') {
        console.warn('文档过大，尝试分割处理');
        return await this.splitAndCreateDocument(documentData);
      } else if (error.errorCode === 'AUTH_TOKEN_EXPIRED') {
        console.warn('令牌过期，需要重新登录');
        // 触发重新登录流程
        this.onTokenExpired();
        throw error;
      } else {
        console.error('创建文档失败:', error.message);
        throw error;
      }
    }
  }
}

class APIError extends Error {
  constructor(errorCode, message, statusCode, details = {}) {
    super(message);
    this.name = 'APIError';
    this.errorCode = errorCode;
    this.statusCode = statusCode;
    this.details = details;
  }
}
```

### 3. 重试机制

```python
import time
import random
from functools import wraps

def retry_on_error(max_retries=3, backoff_factor=1, retry_on_codes=None):
    """错误重试装饰器"""
    if retry_on_codes is None:
        retry_on_codes = [500, 502, 503, 504]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except APIError as e:
                    last_exception = e
                    
                    if e.status_code not in retry_on_codes or attempt == max_retries:
                        raise
                    
                    # 指数退避 + 随机抖动
                    delay = backoff_factor * (2 ** attempt) + random.uniform(0, 1)
                    print(f"请求失败，{delay:.1f}秒后重试 (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(delay)
            
            raise last_exception
        return wrapper
    return decorator

# 使用示例
@retry_on_error(max_retries=3, backoff_factor=0.5)
def search_documents(client, query):
    return client.search(query)
```

### 4. 错误监控和日志

```python
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('master_know_api')

def log_api_error(error: APIError, context: dict = None):
    """记录API错误日志"""
    log_data = {
        'timestamp': datetime.now().isoformat(),
        'error_code': error.error_code,
        'message': error.message,
        'status_code': error.status_code,
        'details': error.details,
        'context': context or {}
    }
    
    if error.status_code >= 500:
        logger.error(f"Server error: {log_data}")
    elif error.status_code >= 400:
        logger.warning(f"Client error: {log_data}")
    else:
        logger.info(f"API error: {log_data}")

# 使用示例
try:
    result = client.create_document(title="测试", content="内容")
except APIError as e:
    log_api_error(e, {'operation': 'create_document', 'user_id': 'user123'})
    raise
```

## 📞 获取帮助

### 错误排查步骤

1. **检查错误码**: 根据错误码查找对应的解决方案
2. **查看错误详情**: 分析 `details` 字段中的具体信息
3. **检查请求格式**: 确认请求参数和格式正确
4. **验证权限**: 确认用户有相应的操作权限
5. **查看系统状态**: 检查服务是否正常运行
6. **联系支持**: 如果问题持续存在，请联系技术支持

### 技术支持

- **文档**: [API文档](./README.md)
- **问题报告**: [GitHub Issues](https://github.com/your-org/master-know/issues)
- **技术支持**: <EMAIL>
- **状态页面**: [系统状态](https://status.master-know.com)
