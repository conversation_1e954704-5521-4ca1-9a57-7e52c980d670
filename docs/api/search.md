# 搜索 API

## 🔍 概述

搜索 API 提供强大的智能搜索功能，支持全文搜索、语义搜索、混合搜索等多种搜索模式，帮助用户快速找到相关内容。

## 🔗 基础路径

```
/api/v1/search
```

## 🎯 搜索类型

### 1. 全文搜索 (Full-text Search)
基于关键词匹配的传统搜索，支持布尔查询、短语查询等。

### 2. 语义搜索 (Semantic Search)  
基于向量相似度的语义搜索，理解查询意图和内容语义。

### 3. 混合搜索 (Hybrid Search)
结合全文搜索和语义搜索的优势，提供最佳搜索体验。

## 📚 API 端点

### 1. 通用搜索

执行智能搜索，自动选择最佳搜索策略。

```http
GET /api/v1/search/
```

**查询参数**:
- `q` (string, 必需): 搜索查询词
- `limit` (int, 可选): 返回结果数量，默认 10，最大 100
- `offset` (int, 可选): 结果偏移量，默认 0
- `search_type` (string, 可选): 搜索类型 (`fulltext`, `semantic`, `hybrid`)，默认 `hybrid`
- `min_score` (float, 可选): 最小相关性分数，默认 0.0
- `document_ids` (string[], 可选): 限制搜索的文档ID列表
- `file_types` (string[], 可选): 限制搜索的文件类型
- `tags` (string[], 可选): 限制搜索的标签

**请求示例**:
```http
GET /api/v1/search/?q=机器学习算法&limit=20&search_type=hybrid&min_score=0.5
```

**响应示例**:
```json
{
  "query": "机器学习算法",
  "search_type": "hybrid",
  "results": [
    {
      "document_id": "123e4567-e89b-12d3-a456-************",
      "chunk_id": "chunk_001",
      "title": "机器学习基础教程",
      "content": "机器学习算法是人工智能的核心组成部分...",
      "score": 0.95,
      "chunk_index": 0,
      "start_char": 0,
      "end_char": 200,
      "file_type": "md",
      "tags": ["机器学习", "算法", "AI"],
      "highlights": [
        "机器学习<em>算法</em>是人工智能的核心"
      ],
      "created_at": "2025-08-16T14:30:00Z"
    }
  ],
  "total": 15,
  "took": 23,
  "max_score": 0.95,
  "aggregations": {
    "file_types": {
      "md": 8,
      "txt": 5,
      "pdf": 2
    },
    "tags": {
      "机器学习": 10,
      "算法": 8,
      "AI": 6
    }
  }
}
```

### 2. 全文搜索

执行基于关键词的全文搜索。

```http
GET /api/v1/search/fulltext
```

**查询参数**:
- `q` (string, 必需): 搜索查询词
- `operator` (string, 可选): 查询操作符 (`AND`, `OR`)，默认 `OR`
- `fuzzy` (boolean, 可选): 是否启用模糊匹配，默认 `false`
- `phrase` (boolean, 可选): 是否进行短语搜索，默认 `false`
- 其他通用参数同上

**请求示例**:
```http
GET /api/v1/search/fulltext?q=深度学习 神经网络&operator=AND&fuzzy=true
```

### 3. 语义搜索

执行基于向量相似度的语义搜索。

```http
GET /api/v1/search/semantic
```

**查询参数**:
- `q` (string, 必需): 搜索查询词
- `embedding_model` (string, 可选): 嵌入模型名称，默认使用系统配置
- `similarity_threshold` (float, 可选): 相似度阈值，默认 0.7
- 其他通用参数同上

**请求示例**:
```http
GET /api/v1/search/semantic?q=如何训练神经网络模型&similarity_threshold=0.8
```

### 4. 混合搜索

结合全文搜索和语义搜索的混合搜索。

```http
GET /api/v1/search/hybrid
```

**查询参数**:
- `q` (string, 必需): 搜索查询词
- `fulltext_weight` (float, 可选): 全文搜索权重，默认 0.5
- `semantic_weight` (float, 可选): 语义搜索权重，默认 0.5
- `rerank` (boolean, 可选): 是否重新排序结果，默认 `true`
- 其他通用参数同上

**请求示例**:
```http
GET /api/v1/search/hybrid?q=Python编程技巧&fulltext_weight=0.3&semantic_weight=0.7
```

### 5. 搜索建议

获取搜索查询建议和自动补全。

```http
GET /api/v1/search/suggestions
```

**查询参数**:
- `q` (string, 必需): 部分查询词
- `limit` (int, 可选): 建议数量，默认 5

**响应示例**:
```json
{
  "query": "机器学",
  "suggestions": [
    {
      "text": "机器学习",
      "score": 0.95,
      "frequency": 150
    },
    {
      "text": "机器学习算法",
      "score": 0.88,
      "frequency": 89
    },
    {
      "text": "机器学习模型",
      "score": 0.82,
      "frequency": 67
    }
  ]
}
```

### 6. 相似文档推荐

基于指定文档推荐相似内容。

```http
GET /api/v1/search/similar/{document_id}
```

**路径参数**:
- `document_id` (UUID): 参考文档ID

**查询参数**:
- `limit` (int, 可选): 推荐数量，默认 10
- `min_similarity` (float, 可选): 最小相似度，默认 0.6

**响应示例**:
```json
{
  "reference_document": {
    "id": "123e4567-e89b-12d3-a456-************",
    "title": "深度学习入门"
  },
  "similar_documents": [
    {
      "document_id": "456e7890-e89b-12d3-a456-************",
      "title": "神经网络基础",
      "similarity": 0.87,
      "common_topics": ["深度学习", "神经网络", "机器学习"]
    }
  ]
}
```

### 7. 搜索历史

获取用户搜索历史记录。

```http
GET /api/v1/search/history
```

**查询参数**:
- `limit` (int, 可选): 历史记录数量，默认 20

**响应示例**:
```json
{
  "history": [
    {
      "query": "机器学习算法",
      "search_type": "hybrid",
      "results_count": 15,
      "searched_at": "2025-08-16T14:30:00Z"
    }
  ]
}
```

### 8. 搜索统计

获取搜索相关的统计信息。

```http
GET /api/v1/search/stats
```

**响应示例**:
```json
{
  "total_documents": 1250,
  "total_chunks": 8500,
  "indexed_documents": 1200,
  "search_stats": {
    "total_searches": 5420,
    "avg_response_time": 45,
    "popular_queries": [
      {"query": "机器学习", "count": 234},
      {"query": "Python编程", "count": 189}
    ]
  },
  "index_stats": {
    "last_updated": "2025-08-16T14:00:00Z",
    "index_size": "2.5GB",
    "documents_pending": 50
  }
}
```

## 📊 数据模型

### SearchResult 模型

```typescript
interface SearchResult {
  document_id: string;           // 文档ID
  chunk_id?: string;             // 分块ID (如果是分块搜索)
  title: string;                 // 文档标题
  content: string;               // 匹配内容
  score: number;                 // 相关性分数 (0-1)
  chunk_index?: number;          // 分块索引
  start_char?: number;           // 起始字符位置
  end_char?: number;             // 结束字符位置
  file_type: string;             // 文件类型
  tags: string[];                // 标签列表
  highlights: string[];          // 高亮片段
  created_at: string;            // 创建时间
}
```

### SearchResponse 模型

```typescript
interface SearchResponse {
  query: string;                 // 搜索查询
  search_type: string;           // 搜索类型
  results: SearchResult[];       // 搜索结果
  total: number;                 // 总结果数
  took: number;                  // 搜索耗时(毫秒)
  max_score: number;             // 最高分数
  aggregations?: {               // 聚合统计
    file_types: Record<string, number>;
    tags: Record<string, number>;
  };
}
```

## ⚡ 性能优化

### 1. 搜索缓存
- 热门查询结果自动缓存
- 缓存有效期：5分钟
- 支持缓存预热

### 2. 索引优化
- 实时索引更新
- 增量索引构建
- 智能索引分片

### 3. 查询优化
- 查询重写和优化
- 自动查询扩展
- 结果去重和合并

## 🔧 高级功能

### 1. 搜索过滤器

```http
GET /api/v1/search/?q=Python&filters={"file_type":["py","md"],"tags":["编程"],"date_range":{"start":"2025-01-01","end":"2025-12-31"}}
```

### 2. 搜索排序

```http
GET /api/v1/search/?q=算法&sort_by=relevance&sort_order=desc
```

支持的排序字段：
- `relevance`: 相关性 (默认)
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `title`: 标题
- `size`: 文件大小

### 3. 搜索高亮

```http
GET /api/v1/search/?q=机器学习&highlight=true&highlight_tags=["<mark>","</mark>"]
```

## 💡 使用示例

### Python 示例

```python
import requests

BASE_URL = "http://localhost:8000/api/v1"
headers = {"Authorization": "Bearer YOUR_TOKEN"}

# 基础搜索
def search_documents(query, search_type="hybrid", limit=10):
    params = {
        "q": query,
        "search_type": search_type,
        "limit": limit,
        "min_score": 0.5
    }
    
    response = requests.get(f"{BASE_URL}/search/", params=params, headers=headers)
    return response.json()

# 搜索结果
results = search_documents("深度学习神经网络")
print(f"找到 {results['total']} 个相关结果")

for result in results['results']:
    print(f"标题: {result['title']}")
    print(f"分数: {result['score']:.2f}")
    print(f"内容: {result['content'][:100]}...")
    print("---")
```

### JavaScript 示例

```javascript
class SearchAPI {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  async search(query, options = {}) {
    const params = new URLSearchParams({
      q: query,
      search_type: options.searchType || 'hybrid',
      limit: options.limit || 10,
      ...options
    });

    const response = await fetch(`${this.baseUrl}/search/?${params}`, {
      headers: this.headers
    });

    return await response.json();
  }

  async getSuggestions(query) {
    const params = new URLSearchParams({ q: query, limit: 5 });
    const response = await fetch(`${this.baseUrl}/search/suggestions?${params}`, {
      headers: this.headers
    });

    return await response.json();
  }
}

// 使用示例
const searchAPI = new SearchAPI('http://localhost:8000/api/v1', 'YOUR_TOKEN');

// 执行搜索
searchAPI.search('Python编程技巧', { 
  searchType: 'hybrid',
  limit: 20,
  minScore: 0.6 
}).then(results => {
  console.log(`找到 ${results.total} 个结果`);
  results.results.forEach(result => {
    console.log(`${result.title}: ${result.score}`);
  });
});
```
