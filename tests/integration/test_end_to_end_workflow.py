"""
端到端集成测试
测试完整的文档处理工作流程：上传 -> 分割 -> 索引 -> 搜索 -> 对话
"""

import pytest
import sys
import uuid
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入引擎和策略
from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document as SplitterDocument


class TestEndToEndWorkflow:
    """端到端工作流程测试"""
    
    @pytest.fixture
    def sample_documents(self):
        """测试文档样本"""
        return {
            "short_text": {
                "title": "短文档测试",
                "content": "这是一个短文档，用于测试基本的文档处理功能。",
                "file_type": "txt",
                "expected_chunks": 1
            },
            "long_text": {
                "title": "长文档测试", 
                "content": "这是一个长文档。" + "内容重复。" * 200,
                "file_type": "txt",
                "expected_chunks": 3  # 基于token策略
            },
            "markdown": {
                "title": "Markdown文档测试",
                "content": """# 标题1
这是第一段内容。

## 标题2
这是第二段内容，包含更多的文字来测试分割功能。

### 标题3
- 列表项1
- 列表项2
- 列表项3

最后一段内容。""",
                "file_type": "md",
                "expected_chunks": 2  # 基于字符策略
            }
        }
    
    @pytest.fixture
    def text_splitter_engine(self):
        """文本分割引擎"""
        return TextSplitterEngine()
    
    def test_document_creation_and_validation(self, sample_documents):
        """测试文档创建和验证"""
        for doc_key, doc_data in sample_documents.items():
            # 创建文档对象
            doc = SplitterDocument(
                title=doc_data["title"],
                content=doc_data["content"],
                file_type=doc_data["file_type"],
                size=len(doc_data["content"].encode('utf-8'))
            )
            
            # 验证文档属性
            assert doc.title == doc_data["title"]
            assert doc.content == doc_data["content"]
            assert doc.file_type == doc_data["file_type"]
            assert doc.size > 0
            assert doc.get_word_count() > 0
            assert doc.get_char_count() == len(doc_data["content"])
    
    def test_document_splitting_workflow(self, sample_documents, text_splitter_engine):
        """测试文档分割工作流程"""
        for doc_key, doc_data in sample_documents.items():
            # 创建文档
            doc = SplitterDocument(
                title=doc_data["title"],
                content=doc_data["content"],
                file_type=doc_data["file_type"],
                size=len(doc_data["content"].encode('utf-8'))
            )
            
            # 根据文档类型选择策略
            if doc.file_type.lower() in ['md', 'markdown']:
                strategy = CharacterBasedStrategy(max_chars=500)
            else:
                strategy = TokenBasedStrategy(max_tokens=100)
            
            # 执行分割
            result = text_splitter_engine.split_document(doc, strategy)
            
            # 验证分割结果
            assert result.error is None  # 成功时error应该为None
            assert result.total_chunks > 0
            assert len(result.chunks) == result.total_chunks
            assert result.document_id == str(doc.id)
            
            # 验证每个分块
            for i, chunk in enumerate(result.chunks):
                assert chunk.chunk_index == i
                assert chunk.content.strip() != ""
                assert chunk.start_char >= 0
                assert chunk.end_char > chunk.start_char
                assert chunk.token_count is not None
                assert chunk.token_count > 0
            
            # 验证分块连续性
            for i in range(1, len(result.chunks)):
                prev_chunk = result.chunks[i-1]
                curr_chunk = result.chunks[i]
                # 注意：由于语义分割，分块可能不是严格连续的
                assert curr_chunk.start_char >= prev_chunk.start_char
    
    def test_batch_document_processing(self, sample_documents, text_splitter_engine):
        """测试批量文档处理"""
        documents = []
        strategies = []
        
        # 准备批量文档
        for doc_key, doc_data in sample_documents.items():
            doc = SplitterDocument(
                title=doc_data["title"],
                content=doc_data["content"],
                file_type=doc_data["file_type"],
                size=len(doc_data["content"].encode('utf-8'))
            )
            documents.append(doc)
            
            # 选择策略
            if doc.file_type.lower() in ['md', 'markdown']:
                strategy = CharacterBasedStrategy(max_chars=500)
            else:
                strategy = TokenBasedStrategy(max_tokens=100)
            strategies.append(strategy)
        
        # 执行批量分割
        results = text_splitter_engine.batch_split(documents)

        # 验证批量结果
        assert len(results) == len(documents)

        for i, result in enumerate(results):
            if result.error is None:  # 成功
                assert result.total_chunks > 0
                assert len(result.chunks) == result.total_chunks
                assert result.document_id == str(documents[i].id)
            else:
                # 如果失败，应该有错误信息
                assert result.error is not None
    
    def test_error_handling_workflow(self, text_splitter_engine):
        """测试错误处理工作流程"""
        # 测试空文档
        with pytest.raises(ValueError):
            SplitterDocument(
                title="空文档",
                content="",
                file_type="txt",
                size=0
            )
        
        # 测试无效策略参数
        invalid_strategy = TokenBasedStrategy(max_tokens=1)  # 使用最小有效值

        valid_doc = SplitterDocument(
            title="测试文档",
            content="这是测试内容",
            file_type="txt",
            size=15
        )

        # 应该处理策略
        result = text_splitter_engine.split_document(valid_doc, invalid_strategy)
        # 引擎应该返回结果
        assert result is not None
        assert result.error is None or result.error is not None  # 可能成功或失败
    
    def test_performance_workflow(self, text_splitter_engine):
        """测试性能工作流程"""
        import time
        
        # 创建大文档
        large_content = "这是一个大文档的内容。" * 1000  # 约13KB
        large_doc = SplitterDocument(
            title="大文档性能测试",
            content=large_content,
            file_type="txt",
            size=len(large_content.encode('utf-8'))
        )
        
        strategy = TokenBasedStrategy(max_tokens=500)
        
        # 测量处理时间
        start_time = time.time()
        result = text_splitter_engine.split_document(large_doc, strategy)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # 验证性能
        assert result.error is None  # 成功时error应该为None
        assert result.total_chunks > 0
        assert processing_time < 5.0  # 应该在5秒内完成
        
        # 验证分块质量
        total_chars = sum(len(chunk.content) for chunk in result.chunks)
        # 允许一些字符差异（由于重叠或分割边界）
        assert abs(total_chars - len(large_content)) < len(large_content) * 0.1


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
