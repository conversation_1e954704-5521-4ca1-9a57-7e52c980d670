"""
API工作流程集成测试
测试完整的API调用流程：认证 -> 文档上传 -> 处理 -> 搜索 -> 对话
"""

import pytest
import sys
import json
import uuid
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class MockResponse:
    """模拟HTTP响应"""
    def __init__(self, status_code, json_data=None, text_data=None):
        self.status_code = status_code
        self._json_data = json_data or {}
        self._text_data = text_data or ""
    
    def json(self):
        return self._json_data
    
    @property
    def text(self):
        return self._text_data


class MockDatabase:
    """模拟数据库"""
    def __init__(self):
        self.documents = {}
        self.chunks = {}
        self.users = {}
        self.conversations = {}
    
    def add_document(self, doc_data):
        doc_id = str(uuid.uuid4())
        self.documents[doc_id] = {
            "id": doc_id,
            "title": doc_data["title"],
            "content": doc_data["content"],
            "file_type": doc_data.get("file_type", "txt"),
            "status": "uploaded",
            "created_at": datetime.now().isoformat(),
            **doc_data
        }
        return self.documents[doc_id]
    
    def get_document(self, doc_id):
        return self.documents.get(doc_id)
    
    def add_chunks(self, doc_id, chunks):
        for i, chunk in enumerate(chunks):
            chunk_id = f"{doc_id}_chunk_{i}"
            self.chunks[chunk_id] = {
                "id": chunk_id,
                "document_id": doc_id,
                "content": chunk["content"],
                "chunk_index": i,
                "start_char": chunk.get("start_char", 0),
                "end_char": chunk.get("end_char", len(chunk["content"])),
                "token_count": chunk.get("token_count", 10)
            }
        return list(self.chunks.values())


class TestAPIWorkflow:
    """API工作流程测试"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库"""
        return MockDatabase()
    
    @pytest.fixture
    def sample_api_data(self):
        """API测试数据"""
        return {
            "user": {
                "email": "<EMAIL>",
                "password": "testpassword",
                "full_name": "Test User"
            },
            "documents": [
                {
                    "title": "API测试文档1",
                    "content": "这是第一个API测试文档的内容。" * 10,
                    "file_type": "txt"
                },
                {
                    "title": "API测试文档2", 
                    "content": """# API测试Markdown文档
                    
## 介绍
这是一个用于测试API的Markdown文档。

## 内容
包含多个段落和格式化文本。

### 列表
- 项目1
- 项目2
- 项目3""",
                    "file_type": "md"
                }
            ],
            "search_queries": [
                "API测试",
                "Markdown",
                "文档内容"
            ],
            "conversation": {
                "title": "测试对话",
                "messages": [
                    {"role": "user", "content": "请总结文档内容"},
                    {"role": "assistant", "content": "这些文档主要用于API测试..."}
                ]
            }
        }
    
    def test_authentication_workflow(self, sample_api_data):
        """测试认证工作流程"""
        user_data = sample_api_data["user"]
        
        # 模拟注册API调用
        with patch('requests.post') as mock_post:
            # 模拟成功注册
            mock_post.return_value = MockResponse(201, {
                "id": str(uuid.uuid4()),
                "email": user_data["email"],
                "full_name": user_data["full_name"],
                "is_active": True
            })
            
            # 验证注册请求
            response = mock_post.return_value
            assert response.status_code == 201
            user_info = response.json()
            assert user_info["email"] == user_data["email"]
            assert user_info["is_active"] is True
        
        # 模拟登录API调用
        with patch('requests.post') as mock_post:
            # 模拟成功登录
            mock_post.return_value = MockResponse(200, {
                "access_token": "test_access_token",
                "token_type": "bearer",
                "expires_in": 3600
            })
            
            # 验证登录请求
            response = mock_post.return_value
            assert response.status_code == 200
            token_info = response.json()
            assert "access_token" in token_info
            assert token_info["token_type"] == "bearer"
    
    def test_document_upload_workflow(self, sample_api_data, mock_db):
        """测试文档上传工作流程"""
        documents = sample_api_data["documents"]
        
        for doc_data in documents:
            # 模拟文档上传API调用
            with patch('requests.post') as mock_post:
                # 添加文档到模拟数据库
                created_doc = mock_db.add_document(doc_data)
                
                # 模拟成功上传响应
                mock_post.return_value = MockResponse(201, created_doc)
                
                # 验证上传请求
                response = mock_post.return_value
                assert response.status_code == 201
                doc_info = response.json()
                assert doc_info["title"] == doc_data["title"]
                assert doc_info["status"] == "uploaded"
                assert "id" in doc_info
    
    def test_document_processing_workflow(self, sample_api_data, mock_db):
        """测试文档处理工作流程"""
        documents = sample_api_data["documents"]
        
        for doc_data in documents:
            # 创建文档
            created_doc = mock_db.add_document(doc_data)
            doc_id = created_doc["id"]
            
            # 模拟文档处理API调用
            with patch('requests.post') as mock_post:
                # 模拟分块数据
                mock_chunks = [
                    {
                        "content": doc_data["content"][:100],
                        "chunk_index": 0,
                        "start_char": 0,
                        "end_char": 100,
                        "token_count": 20
                    },
                    {
                        "content": doc_data["content"][100:],
                        "chunk_index": 1,
                        "start_char": 100,
                        "end_char": len(doc_data["content"]),
                        "token_count": 15
                    }
                ]
                
                # 添加分块到模拟数据库
                mock_db.add_chunks(doc_id, mock_chunks)
                
                # 模拟处理成功响应
                mock_post.return_value = MockResponse(200, {
                    "success": True,
                    "document_id": doc_id,
                    "chunks_created": len(mock_chunks),
                    "message": f"Successfully processed document '{doc_data['title']}'"
                })
                
                # 验证处理请求
                response = mock_post.return_value
                assert response.status_code == 200
                result = response.json()
                assert result["success"] is True
                assert result["chunks_created"] > 0
                assert doc_data['title'] in result["message"]
    
    def test_search_workflow(self, sample_api_data, mock_db):
        """测试搜索工作流程"""
        # 先创建一些文档和分块
        for doc_data in sample_api_data["documents"]:
            created_doc = mock_db.add_document(doc_data)
            mock_chunks = [
                {
                    "content": doc_data["content"][:200],
                    "chunk_index": 0,
                    "start_char": 0,
                    "end_char": 200,
                    "token_count": 40
                }
            ]
            mock_db.add_chunks(created_doc["id"], mock_chunks)
        
        # 测试搜索查询
        for query in sample_api_data["search_queries"]:
            with patch('requests.get') as mock_get:
                # 模拟搜索结果
                mock_results = [
                    {
                        "document_id": list(mock_db.documents.keys())[0],
                        "title": "API测试文档1",
                        "content": "这是第一个API测试文档的内容...",
                        "score": 0.95,
                        "chunk_index": 0
                    }
                ]
                
                mock_get.return_value = MockResponse(200, {
                    "query": query,
                    "results": mock_results,
                    "total": len(mock_results),
                    "took": 15
                })
                
                # 验证搜索请求
                response = mock_get.return_value
                assert response.status_code == 200
                search_result = response.json()
                assert search_result["query"] == query
                assert len(search_result["results"]) > 0
                assert search_result["total"] > 0
    
    def test_conversation_workflow(self, sample_api_data, mock_db):
        """测试对话工作流程"""
        conversation_data = sample_api_data["conversation"]
        
        # 模拟创建对话
        with patch('requests.post') as mock_post:
            conversation_id = str(uuid.uuid4())
            mock_post.return_value = MockResponse(201, {
                "id": conversation_id,
                "title": conversation_data["title"],
                "status": "active",
                "created_at": datetime.now().isoformat()
            })
            
            # 验证对话创建
            response = mock_post.return_value
            assert response.status_code == 201
            conv_info = response.json()
            assert conv_info["title"] == conversation_data["title"]
            assert conv_info["status"] == "active"
        
        # 模拟发送消息
        for message in conversation_data["messages"]:
            with patch('requests.post') as mock_post:
                mock_post.return_value = MockResponse(200, {
                    "id": str(uuid.uuid4()),
                    "conversation_id": conversation_id,
                    "role": message["role"],
                    "content": message["content"],
                    "timestamp": datetime.now().isoformat()
                })
                
                # 验证消息发送
                response = mock_post.return_value
                assert response.status_code == 200
                msg_info = response.json()
                assert msg_info["role"] == message["role"]
                assert msg_info["content"] == message["content"]
    
    def test_complete_workflow_integration(self, sample_api_data, mock_db):
        """测试完整工作流程集成"""
        # 1. 认证
        access_token = "test_access_token"
        
        # 2. 上传文档
        doc_data = sample_api_data["documents"][0]
        created_doc = mock_db.add_document(doc_data)
        doc_id = created_doc["id"]
        
        # 3. 处理文档
        mock_chunks = [{"content": doc_data["content"], "chunk_index": 0}]
        mock_db.add_chunks(doc_id, mock_chunks)
        
        # 4. 搜索文档
        query = sample_api_data["search_queries"][0]
        
        # 5. 创建对话
        conversation_id = str(uuid.uuid4())
        
        # 验证整个流程的数据一致性
        assert doc_id in mock_db.documents
        assert len(mock_db.chunks) > 0
        assert mock_db.get_document(doc_id)["title"] == doc_data["title"]
        
        # 验证分块与文档的关联
        doc_chunks = [chunk for chunk in mock_db.chunks.values() 
                     if chunk["document_id"] == doc_id]
        assert len(doc_chunks) > 0
        assert doc_chunks[0]["content"] == doc_data["content"]


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
