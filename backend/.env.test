# Test environment configuration
PROJECT_NAME=Master-Know-Test
STACK_NAME=master-know-test
ENVIRONMENT=test

# Security
SECRET_KEY=test-secret-key-for-testing-only
ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_ALGORITHM=HS256
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=testpassword

# Database (SQLite for testing)
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=test_master_know
POSTGRES_USER=test_user
POSTGRES_PASSWORD=testpassword

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1

# Manticore
MANTICORE_HOST=localhost
MANTICORE_HTTP_PORT=9308

# Other settings
DOMAIN=localhost
FRONTEND_HOST=http://localhost:3000
BACKEND_CORS_ORIGINS="http://localhost:3000"
