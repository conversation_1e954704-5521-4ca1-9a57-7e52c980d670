"""
安全验证器和输入过滤器
提供输入验证、XSS防护、SQL注入防护等安全功能
"""

import re
import html
import bleach
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
from fastapi import HTTPException, status

from app.core.logging import get_logger

logger = get_logger(__name__)


class SecurityConfig:
    """安全配置"""
    
    # XSS防护配置
    ALLOWED_HTML_TAGS = [
        'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'code', 'pre'
    ]
    
    ALLOWED_HTML_ATTRIBUTES = {
        '*': ['class'],
        'a': ['href', 'title'],
        'img': ['src', 'alt', 'width', 'height']
    }
    
    # 文件上传配置
    ALLOWED_FILE_EXTENSIONS = {'.txt', '.md', '.pdf', '.docx', '.doc', '.rtf'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    # 密码策略
    MIN_PASSWORD_LENGTH = 8
    MAX_PASSWORD_LENGTH = 128
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL_CHARS = True
    
    # 速率限制
    DEFAULT_RATE_LIMIT = "100/minute"
    LOGIN_RATE_LIMIT = "5/minute"
    SEARCH_RATE_LIMIT = "50/minute"
    
    # 敏感数据模式
    SENSITIVE_PATTERNS = [
        r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # 信用卡号
        r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # 邮箱
        r'\b\d{11}\b',  # 手机号
    ]


class InputSanitizer:
    """输入清理器"""
    
    @staticmethod
    def sanitize_html(content: str, strip_tags: bool = False) -> str:
        """清理HTML内容，防止XSS攻击"""
        if not content:
            return content
        
        if strip_tags:
            # 完全移除HTML标签
            return bleach.clean(content, tags=[], strip=True)
        else:
            # 只允许安全的HTML标签
            return bleach.clean(
                content,
                tags=SecurityConfig.ALLOWED_HTML_TAGS,
                attributes=SecurityConfig.ALLOWED_HTML_ATTRIBUTES,
                strip=True
            )
    
    @staticmethod
    def sanitize_sql_input(value: str) -> str:
        """清理SQL输入，防止SQL注入"""
        if not isinstance(value, str):
            return value
        
        # 移除或转义危险字符
        dangerous_patterns = [
            r"[';\"\\]",  # 引号和反斜杠
            r"--",        # SQL注释
            r"/\*.*?\*/", # 多行注释
            r"\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b"  # SQL关键词
        ]
        
        cleaned_value = value
        for pattern in dangerous_patterns:
            cleaned_value = re.sub(pattern, "", cleaned_value, flags=re.IGNORECASE)
        
        return cleaned_value.strip()
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，防止路径遍历攻击"""
        if not filename:
            return filename
        
        # 移除路径分隔符和特殊字符
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        filename = re.sub(r'\.\.', '', filename)  # 移除路径遍历
        filename = filename.strip('. ')  # 移除前后的点和空格
        
        # 限制长度
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:250] + ('.' + ext if ext else '')
        
        return filename
    
    @staticmethod
    def mask_sensitive_data(text: str) -> str:
        """遮蔽敏感数据"""
        if not text:
            return text
        
        masked_text = text
        for pattern in SecurityConfig.SENSITIVE_PATTERNS:
            masked_text = re.sub(pattern, lambda m: '*' * len(m.group()), masked_text)
        
        return masked_text


class PasswordValidator:
    """密码验证器"""
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """验证密码强度"""
        errors = []
        score = 0
        
        if len(password) < SecurityConfig.MIN_PASSWORD_LENGTH:
            errors.append(f"密码长度至少需要{SecurityConfig.MIN_PASSWORD_LENGTH}个字符")
        elif len(password) >= SecurityConfig.MIN_PASSWORD_LENGTH:
            score += 1
        
        if len(password) > SecurityConfig.MAX_PASSWORD_LENGTH:
            errors.append(f"密码长度不能超过{SecurityConfig.MAX_PASSWORD_LENGTH}个字符")
        
        if SecurityConfig.REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
            errors.append("密码必须包含至少一个大写字母")
        elif re.search(r'[A-Z]', password):
            score += 1
        
        if SecurityConfig.REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
            errors.append("密码必须包含至少一个小写字母")
        elif re.search(r'[a-z]', password):
            score += 1
        
        if SecurityConfig.REQUIRE_DIGITS and not re.search(r'\d', password):
            errors.append("密码必须包含至少一个数字")
        elif re.search(r'\d', password):
            score += 1
        
        if SecurityConfig.REQUIRE_SPECIAL_CHARS and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含至少一个特殊字符")
        elif re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        
        # 检查常见弱密码
        weak_passwords = [
            'password', '123456', 'qwerty', 'abc123', 'password123',
            'admin', 'root', 'user', 'test', 'guest'
        ]
        if password.lower() in weak_passwords:
            errors.append("密码过于简单，请使用更复杂的密码")
            score = 0
        
        # 检查重复字符
        if len(set(password)) < len(password) * 0.6:
            errors.append("密码包含过多重复字符")
            score -= 1
        
        strength_level = "weak"
        if score >= 4:
            strength_level = "strong"
        elif score >= 2:
            strength_level = "medium"
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "score": max(0, score),
            "strength": strength_level
        }


class FileValidator:
    """文件验证器"""
    
    @staticmethod
    def validate_file_upload(filename: str, content: bytes, content_type: str) -> Dict[str, Any]:
        """验证文件上传"""
        errors = []
        
        # 验证文件名
        if not filename:
            errors.append("文件名不能为空")
        else:
            # 检查文件扩展名
            file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
            if f'.{file_ext}' not in SecurityConfig.ALLOWED_FILE_EXTENSIONS:
                errors.append(f"不支持的文件类型: {file_ext}")
            
            # 检查文件名安全性
            sanitized_filename = InputSanitizer.sanitize_filename(filename)
            if sanitized_filename != filename:
                errors.append("文件名包含不安全字符")
        
        # 验证文件大小
        if len(content) > SecurityConfig.MAX_FILE_SIZE:
            errors.append(f"文件大小超过限制 ({SecurityConfig.MAX_FILE_SIZE / 1024 / 1024:.1f}MB)")
        
        # 验证文件内容类型
        allowed_content_types = {
            'text/plain', 'text/markdown', 'application/pdf',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
        if content_type not in allowed_content_types:
            errors.append(f"不支持的内容类型: {content_type}")
        
        # 检查文件头部（魔数）
        if not FileValidator._validate_file_signature(content, file_ext):
            errors.append("文件内容与扩展名不匹配")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "sanitized_filename": InputSanitizer.sanitize_filename(filename)
        }
    
    @staticmethod
    def _validate_file_signature(content: bytes, file_ext: str) -> bool:
        """验证文件签名（魔数）"""
        if not content:
            return False
        
        # 文件签名映射
        signatures = {
            'pdf': [b'%PDF'],
            'docx': [b'PK\x03\x04'],
            'doc': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
            'txt': [],  # 文本文件没有固定签名
            'md': [],   # Markdown文件没有固定签名
        }
        
        expected_signatures = signatures.get(file_ext, [])
        if not expected_signatures:
            return True  # 对于没有固定签名的文件类型，跳过验证
        
        return any(content.startswith(sig) for sig in expected_signatures)


class SecurityMiddleware:
    """安全中间件辅助类"""
    
    @staticmethod
    def validate_request_headers(headers: Dict[str, str]) -> List[str]:
        """验证请求头"""
        warnings = []
        
        # 检查必要的安全头
        if 'user-agent' not in headers:
            warnings.append("缺少User-Agent头")
        
        # 检查可疑的头部
        suspicious_headers = ['x-forwarded-for', 'x-real-ip']
        for header in suspicious_headers:
            if header in headers:
                value = headers[header]
                if not re.match(r'^[\d\.,\s]+$', value):
                    warnings.append(f"可疑的{header}头: {value}")
        
        return warnings
    
    @staticmethod
    def detect_attack_patterns(content: str) -> List[str]:
        """检测攻击模式"""
        if not content:
            return []
        
        attack_patterns = [
            (r'<script[^>]*>.*?</script>', 'XSS攻击'),
            (r'javascript:', 'JavaScript注入'),
            (r'on\w+\s*=', 'HTML事件注入'),
            (r'union\s+select', 'SQL注入'),
            (r'drop\s+table', 'SQL注入'),
            (r'\.\./', '路径遍历'),
            (r'<\?php', 'PHP代码注入'),
            (r'eval\s*\(', '代码执行'),
            (r'exec\s*\(', '命令执行'),
        ]
        
        detected_attacks = []
        content_lower = content.lower()
        
        for pattern, attack_type in attack_patterns:
            if re.search(pattern, content_lower, re.IGNORECASE | re.DOTALL):
                detected_attacks.append(attack_type)
        
        return detected_attacks


# Pydantic验证器
class SecureString(str):
    """安全字符串类型"""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        
        # 清理输入
        sanitized = InputSanitizer.sanitize_html(v, strip_tags=True)
        
        # 检测攻击模式
        attacks = SecurityMiddleware.detect_attack_patterns(v)
        if attacks:
            logger.warning(f"Detected potential attacks: {attacks}", input_content=v[:100])
            raise ValueError(f'Input contains potential security threats: {", ".join(attacks)}')
        
        return sanitized


class SecureFilename(str):
    """安全文件名类型"""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        
        sanitized = InputSanitizer.sanitize_filename(v)
        if not sanitized:
            raise ValueError('Invalid filename')
        
        return sanitized


# 安全验证装饰器
def require_secure_input(func):
    """要求安全输入的装饰器"""
    def wrapper(*args, **kwargs):
        # 检查所有字符串参数
        for arg in args:
            if isinstance(arg, str):
                attacks = SecurityMiddleware.detect_attack_patterns(arg)
                if attacks:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Input contains security threats: {', '.join(attacks)}"
                    )
        
        for key, value in kwargs.items():
            if isinstance(value, str):
                attacks = SecurityMiddleware.detect_attack_patterns(value)
                if attacks:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Parameter '{key}' contains security threats: {', '.join(attacks)}"
                    )
        
        return func(*args, **kwargs)
    return wrapper


def log_security_event(event_type: str, details: Dict[str, Any]):
    """记录安全事件"""
    logger.warning(
        f"Security event: {event_type}",
        event_type=event_type,
        security_event=True,
        **details
    )
