"""
结构化日志配置
提供统一的日志格式、级别管理和输出配置
"""

import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path

from app.core.config import settings


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON格式"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_data.update(record.extra_fields)
        
        # 添加请求相关信息
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        
        if hasattr(record, 'endpoint'):
            log_data["endpoint"] = record.endpoint
        
        if hasattr(record, 'method'):
            log_data["method"] = record.method
        
        if hasattr(record, 'status_code'):
            log_data["status_code"] = record.status_code
        
        if hasattr(record, 'response_time'):
            log_data["response_time"] = record.response_time
        
        return json.dumps(log_data, ensure_ascii=False)


class ContextualLogger:
    """上下文感知的日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.context: Dict[str, Any] = {}
    
    def set_context(self, **kwargs):
        """设置日志上下文"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """清除日志上下文"""
        self.context.clear()
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """带上下文的日志记录"""
        extra_fields = {**self.context, **kwargs}
        extra = {"extra_fields": extra_fields} if extra_fields else {}
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self._log_with_context(logging.CRITICAL, message, **kwargs)


def setup_logging():
    """配置应用日志系统"""
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 根日志配置
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    if settings.LOG_FORMAT.lower() == "json":
        console_handler.setFormatter(StructuredFormatter())
    else:
        console_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        )
    root_logger.addHandler(console_handler)
    
    # 文件处理器 (仅在生产环境)
    if settings.ENVIRONMENT != "local":
        # 应用日志文件
        app_handler = logging.handlers.RotatingFileHandler(
            log_dir / "app.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        app_handler.setFormatter(StructuredFormatter())
        app_handler.setLevel(logging.INFO)
        root_logger.addHandler(app_handler)
        
        # 错误日志文件
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / "error.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10
        )
        error_handler.setFormatter(StructuredFormatter())
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
    
    # 配置特定日志记录器
    configure_loggers()


def configure_loggers():
    """配置特定的日志记录器"""
    
    # 数据库日志
    db_logger = logging.getLogger("sqlalchemy.engine")
    if settings.DATABASE_ECHO:
        db_logger.setLevel(logging.INFO)
    else:
        db_logger.setLevel(logging.WARNING)
    
    # HTTP客户端日志
    http_logger = logging.getLogger("httpx")
    http_logger.setLevel(logging.WARNING)
    
    # 第三方库日志级别
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.INFO)


# 预定义的日志记录器
app_logger = ContextualLogger("app")
api_logger = ContextualLogger("api")
db_logger = ContextualLogger("database")
cache_logger = ContextualLogger("cache")
search_logger = ContextualLogger("search")
auth_logger = ContextualLogger("auth")
document_logger = ContextualLogger("document")
conversation_logger = ContextualLogger("conversation")


def get_logger(name: str) -> ContextualLogger:
    """获取命名的上下文日志记录器"""
    return ContextualLogger(name)


class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self) -> ContextualLogger:
        """获取类专用的日志记录器"""
        if not hasattr(self, '_logger'):
            self._logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        return self._logger


# 日志装饰器
def log_function_call(logger: Optional[ContextualLogger] = None):
    """记录函数调用的装饰器"""
    def decorator(func):
        nonlocal logger
        if logger is None:
            logger = get_logger(func.__module__)
        
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger.info(
                f"Calling function {func.__name__}",
                function=func.__name__,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys())
            )
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"Function {func.__name__} completed successfully",
                    function=func.__name__,
                    execution_time=execution_time
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Function {func.__name__} failed",
                    function=func.__name__,
                    execution_time=execution_time,
                    error=str(e),
                    error_type=type(e).__name__
                )
                raise
        
        return wrapper
    return decorator


def log_database_operation(operation: str):
    """记录数据库操作的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            db_logger.info(
                f"Starting database operation: {operation}",
                operation=operation,
                function=func.__name__
            )
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                db_logger.info(
                    f"Database operation completed: {operation}",
                    operation=operation,
                    function=func.__name__,
                    execution_time=execution_time
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                db_logger.error(
                    f"Database operation failed: {operation}",
                    operation=operation,
                    function=func.__name__,
                    execution_time=execution_time,
                    error=str(e),
                    error_type=type(e).__name__
                )
                raise
        
        return wrapper
    return decorator


# 性能监控日志
def log_performance_metric(metric_name: str, value: float, unit: str = "ms", **tags):
    """记录性能指标"""
    app_logger.info(
        f"Performance metric: {metric_name}",
        metric_name=metric_name,
        metric_value=value,
        metric_unit=unit,
        **tags
    )


# 业务事件日志
def log_business_event(event_type: str, event_data: Dict[str, Any], user_id: Optional[str] = None):
    """记录业务事件"""
    app_logger.info(
        f"Business event: {event_type}",
        event_type=event_type,
        event_data=event_data,
        user_id=user_id
    )


# 安全事件日志
def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "info"):
    """记录安全事件"""
    log_func = getattr(auth_logger, severity.lower(), auth_logger.info)
    log_func(
        f"Security event: {event_type}",
        event_type=event_type,
        security_event=True,
        **details
    )


if __name__ == "__main__":
    # 测试日志配置
    setup_logging()
    
    # 测试不同类型的日志
    app_logger.info("Application started", version="1.0.0")
    api_logger.warning("API rate limit approaching", current_rate=95, limit=100)
    db_logger.error("Database connection failed", host="localhost", port=5432)
    
    # 测试上下文日志
    api_logger.set_context(request_id="req-123", user_id="user-456")
    api_logger.info("Processing user request")
    api_logger.clear_context()
    
    print("日志配置测试完成")
