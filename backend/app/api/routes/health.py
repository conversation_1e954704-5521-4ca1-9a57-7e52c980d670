"""
健康检查和系统状态API
提供系统健康状态、依赖服务检查和诊断信息
"""

import asyncio
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db
from app.core.config import settings
from app.core.logging import get_logger
from app.middleware.performance import get_performance_stats
from app.middleware.error_handler import get_error_stats

router = APIRouter()
logger = get_logger(__name__)

# 系统启动时间
STARTUP_TIME = datetime.now()


@router.get("/health", summary="基础健康检查")
async def health_check() -> Dict[str, Any]:
    """
    基础健康检查端点
    返回系统基本状态信息
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "uptime": str(datetime.now() - STARTUP_TIME),
        "environment": settings.ENVIRONMENT
    }


@router.get("/health/detailed", summary="详细健康检查")
async def detailed_health_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    详细健康检查，包含所有依赖服务状态
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "uptime": str(datetime.now() - STARTUP_TIME),
        "environment": settings.ENVIRONMENT,
        "checks": {}
    }
    
    # 检查数据库连接
    db_status = await check_database_health(db)
    health_status["checks"]["database"] = db_status
    
    # 检查Redis连接
    redis_status = await check_redis_health()
    health_status["checks"]["redis"] = redis_status
    
    # 检查Manticore搜索
    manticore_status = await check_manticore_health()
    health_status["checks"]["manticore"] = manticore_status
    
    # 检查系统资源
    system_status = check_system_resources()
    health_status["checks"]["system"] = system_status
    
    # 检查磁盘空间
    disk_status = check_disk_space()
    health_status["checks"]["disk"] = disk_status
    
    # 确定整体状态
    all_healthy = all(
        check.get("status") == "healthy" 
        for check in health_status["checks"].values()
    )
    
    if not all_healthy:
        health_status["status"] = "unhealthy"
        # 如果有关键服务不健康，返回503状态码
        critical_services = ["database"]
        critical_unhealthy = any(
            health_status["checks"].get(service, {}).get("status") != "healthy"
            for service in critical_services
        )
        if critical_unhealthy:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_status
            )
    
    return health_status


@router.get("/health/readiness", summary="就绪检查")
async def readiness_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    就绪检查 - 确认服务可以处理请求
    """
    checks = {}
    
    # 数据库连接检查
    try:
        await db.execute(text("SELECT 1"))
        checks["database"] = {"status": "ready", "message": "Database connection successful"}
    except Exception as e:
        checks["database"] = {"status": "not_ready", "message": f"Database error: {str(e)}"}
    
    # 检查关键表是否存在
    try:
        await db.execute(text("SELECT COUNT(*) FROM users LIMIT 1"))
        checks["database_schema"] = {"status": "ready", "message": "Database schema available"}
    except Exception as e:
        checks["database_schema"] = {"status": "not_ready", "message": f"Schema error: {str(e)}"}
    
    all_ready = all(check.get("status") == "ready" for check in checks.values())
    
    result = {
        "status": "ready" if all_ready else "not_ready",
        "timestamp": datetime.now().isoformat(),
        "checks": checks
    }
    
    if not all_ready:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=result
        )
    
    return result


@router.get("/health/liveness", summary="存活检查")
async def liveness_check() -> Dict[str, Any]:
    """
    存活检查 - 确认服务进程正在运行
    """
    return {
        "status": "alive",
        "timestamp": datetime.now().isoformat(),
        "pid": psutil.Process().pid,
        "uptime": str(datetime.now() - STARTUP_TIME)
    }


@router.get("/metrics", summary="系统指标")
async def get_metrics() -> Dict[str, Any]:
    """
    获取系统性能指标和统计信息
    """
    # 性能统计
    perf_stats = get_performance_stats()
    
    # 错误统计
    error_stats = get_error_stats()
    
    # 系统资源
    system_metrics = {
        "cpu": {
            "percent": psutil.cpu_percent(interval=1),
            "count": psutil.cpu_count(),
            "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        },
        "memory": {
            "percent": psutil.virtual_memory().percent,
            "available": psutil.virtual_memory().available,
            "total": psutil.virtual_memory().total,
            "used": psutil.virtual_memory().used
        },
        "disk": {
            "percent": psutil.disk_usage('/').percent,
            "free": psutil.disk_usage('/').free,
            "total": psutil.disk_usage('/').total,
            "used": psutil.disk_usage('/').used
        }
    }
    
    # 进程信息
    process = psutil.Process()
    process_metrics = {
        "pid": process.pid,
        "cpu_percent": process.cpu_percent(),
        "memory_percent": process.memory_percent(),
        "memory_info": process.memory_info()._asdict(),
        "num_threads": process.num_threads(),
        "create_time": datetime.fromtimestamp(process.create_time()).isoformat()
    }
    
    return {
        "timestamp": datetime.now().isoformat(),
        "uptime": str(datetime.now() - STARTUP_TIME),
        "performance": perf_stats,
        "errors": error_stats,
        "system": system_metrics,
        "process": process_metrics
    }


async def check_database_health(db: AsyncSession) -> Dict[str, Any]:
    """检查数据库健康状态"""
    try:
        start_time = time.time()
        result = await db.execute(text("SELECT 1"))
        response_time = (time.time() - start_time) * 1000
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "message": "Database connection successful"
        }
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Database connection failed"
        }


async def check_redis_health() -> Dict[str, Any]:
    """检查Redis健康状态"""
    try:
        # 这里应该实际连接Redis进行检查
        # 暂时返回模拟状态
        return {
            "status": "healthy",
            "message": "Redis connection successful"
        }
    except Exception as e:
        logger.error("Redis health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Redis connection failed"
        }


async def check_manticore_health() -> Dict[str, Any]:
    """检查Manticore搜索健康状态"""
    try:
        # 这里应该实际连接Manticore进行检查
        # 暂时返回模拟状态
        return {
            "status": "healthy",
            "message": "Manticore search available"
        }
    except Exception as e:
        logger.error("Manticore health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Manticore search unavailable"
        }


def check_system_resources() -> Dict[str, Any]:
    """检查系统资源状态"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # 定义阈值
        cpu_warning_threshold = 80
        cpu_critical_threshold = 95
        memory_warning_threshold = 80
        memory_critical_threshold = 95
        
        status = "healthy"
        warnings = []
        
        if cpu_percent > cpu_critical_threshold:
            status = "critical"
            warnings.append(f"CPU usage critical: {cpu_percent}%")
        elif cpu_percent > cpu_warning_threshold:
            status = "warning"
            warnings.append(f"CPU usage high: {cpu_percent}%")
        
        if memory.percent > memory_critical_threshold:
            status = "critical"
            warnings.append(f"Memory usage critical: {memory.percent}%")
        elif memory.percent > memory_warning_threshold:
            if status != "critical":
                status = "warning"
            warnings.append(f"Memory usage high: {memory.percent}%")
        
        return {
            "status": status,
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "warnings": warnings
        }
    except Exception as e:
        logger.error("System resource check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Failed to check system resources"
        }


def check_disk_space() -> Dict[str, Any]:
    """检查磁盘空间状态"""
    try:
        disk_usage = psutil.disk_usage('/')
        percent_used = (disk_usage.used / disk_usage.total) * 100
        
        # 定义阈值
        warning_threshold = 80
        critical_threshold = 95
        
        status = "healthy"
        if percent_used > critical_threshold:
            status = "critical"
        elif percent_used > warning_threshold:
            status = "warning"
        
        return {
            "status": status,
            "percent_used": round(percent_used, 2),
            "free_gb": round(disk_usage.free / (1024**3), 2),
            "total_gb": round(disk_usage.total / (1024**3), 2),
            "used_gb": round(disk_usage.used / (1024**3), 2)
        }
    except Exception as e:
        logger.error("Disk space check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Failed to check disk space"
        }
