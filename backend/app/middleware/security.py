"""
安全中间件
提供请求安全检查、速率限制、安全头设置等功能
"""

import time
import json
from typing import Dict, Any, Optional, Set
from collections import defaultdict, deque
from datetime import datetime, timedelta
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.config import settings
from app.core.logging import get_logger
from app.core.security_validators import (
    SecurityMiddleware as SecurityValidator,
    InputSanitizer,
    log_security_event
)

logger = get_logger(__name__)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self.requests = defaultdict(deque)
        self.blocked_ips = {}  # IP -> 解封时间
        self.cleanup_interval = 300  # 5分钟清理一次
        self.last_cleanup = time.time()
    
    def is_allowed(self, identifier: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        now = time.time()
        
        # 清理过期数据
        if now - self.last_cleanup > self.cleanup_interval:
            self._cleanup_expired_data(now)
            self.last_cleanup = now
        
        # 检查IP是否被封禁
        if identifier in self.blocked_ips:
            if now < self.blocked_ips[identifier]:
                return False
            else:
                del self.blocked_ips[identifier]
        
        # 获取时间窗口内的请求
        requests = self.requests[identifier]
        
        # 移除过期请求
        while requests and requests[0] < now - window:
            requests.popleft()
        
        # 检查是否超过限制
        if len(requests) >= limit:
            # 如果严重超限，临时封禁IP
            if len(requests) > limit * 2:
                self.blocked_ips[identifier] = now + 300  # 封禁5分钟
                log_security_event("rate_limit_exceeded", {
                    "identifier": identifier,
                    "requests_count": len(requests),
                    "limit": limit,
                    "blocked_until": datetime.fromtimestamp(now + 300).isoformat()
                })
            return False
        
        # 记录请求
        requests.append(now)
        return True
    
    def _cleanup_expired_data(self, now: float):
        """清理过期数据"""
        # 清理过期的请求记录
        expired_identifiers = []
        for identifier, requests in self.requests.items():
            while requests and requests[0] < now - 3600:  # 清理1小时前的记录
                requests.popleft()
            if not requests:
                expired_identifiers.append(identifier)
        
        for identifier in expired_identifiers:
            del self.requests[identifier]
        
        # 清理过期的封禁记录
        expired_blocks = [ip for ip, unblock_time in self.blocked_ips.items() if now >= unblock_time]
        for ip in expired_blocks:
            del self.blocked_ips[ip]


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.rate_limiter = RateLimiter()
        
        # 安全配置
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
        }
        
        # 敏感端点配置
        self.sensitive_endpoints = {
            "/api/v1/login/access-token": {"rate_limit": (5, 300)},  # 5次/5分钟
            "/api/v1/users/": {"rate_limit": (10, 3600)},  # 10次/小时
            "/api/v1/documents/": {"rate_limit": (50, 3600)},  # 50次/小时
            "/api/v1/search/": {"rate_limit": (100, 3600)},  # 100次/小时
        }
        
        # 可疑活动检测
        self.suspicious_patterns = [
            r'\.\./',  # 路径遍历
            r'<script',  # XSS
            r'union\s+select',  # SQL注入
            r'drop\s+table',  # SQL注入
            r'exec\s*\(',  # 命令执行
        ]
    
    async def dispatch(self, request: Request, call_next):
        """处理请求安全检查"""
        start_time = time.time()
        
        try:
            # 1. 获取客户端标识
            client_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # 2. 安全检查
            security_check_result = await self._perform_security_checks(request, client_ip)
            if security_check_result:
                return security_check_result
            
            # 3. 速率限制检查
            rate_limit_result = self._check_rate_limits(request, client_ip)
            if rate_limit_result:
                return rate_limit_result
            
            # 4. 处理请求
            response = await call_next(request)
            
            # 5. 添加安全头
            self._add_security_headers(response)
            
            # 6. 记录请求日志
            process_time = time.time() - start_time
            await self._log_request(request, response, client_ip, process_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {str(e)}", 
                        client_ip=client_ip, 
                        path=request.url.path)
            # 继续处理，不因安全中间件错误阻断正常请求
            response = await call_next(request)
            self._add_security_headers(response)
            return response
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # 取第一个IP（原始客户端IP）
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip.strip()
        
        # 从连接信息获取
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    async def _perform_security_checks(self, request: Request, client_ip: str) -> Optional[Response]:
        """执行安全检查"""
        
        # 1. 检查请求头
        header_warnings = SecurityValidator.validate_request_headers(dict(request.headers))
        if header_warnings:
            logger.warning("Suspicious request headers", 
                          client_ip=client_ip, 
                          warnings=header_warnings)
        
        # 2. 检查URL路径
        path = request.url.path
        if self._is_suspicious_path(path):
            log_security_event("suspicious_path_access", {
                "client_ip": client_ip,
                "path": path,
                "user_agent": request.headers.get("user-agent", "")
            })
            return Response(
                content=json.dumps({"error": "Access denied"}),
                status_code=status.HTTP_403_FORBIDDEN,
                media_type="application/json"
            )
        
        # 3. 检查查询参数
        query_params = str(request.query_params)
        if query_params:
            attacks = SecurityValidator.detect_attack_patterns(query_params)
            if attacks:
                log_security_event("malicious_query_params", {
                    "client_ip": client_ip,
                    "attacks": attacks,
                    "query_params": query_params[:200]
                })
                return Response(
                    content=json.dumps({"error": "Malicious request detected"}),
                    status_code=status.HTTP_400_BAD_REQUEST,
                    media_type="application/json"
                )
        
        # 4. 检查请求体（如果是POST/PUT请求）
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if "application/json" in content_type:
                try:
                    body = await request.body()
                    if body:
                        body_str = body.decode("utf-8")
                        attacks = SecurityValidator.detect_attack_patterns(body_str)
                        if attacks:
                            log_security_event("malicious_request_body", {
                                "client_ip": client_ip,
                                "attacks": attacks,
                                "content_preview": body_str[:200]
                            })
                            return Response(
                                content=json.dumps({"error": "Malicious content detected"}),
                                status_code=status.HTTP_400_BAD_REQUEST,
                                media_type="application/json"
                            )
                except Exception as e:
                    logger.warning(f"Failed to check request body: {e}")
        
        return None
    
    def _check_rate_limits(self, request: Request, client_ip: str) -> Optional[Response]:
        """检查速率限制"""
        path = request.url.path
        
        # 检查特定端点的速率限制
        for endpoint, config in self.sensitive_endpoints.items():
            if path.startswith(endpoint):
                limit, window = config["rate_limit"]
                if not self.rate_limiter.is_allowed(client_ip, limit, window):
                    log_security_event("rate_limit_exceeded", {
                        "client_ip": client_ip,
                        "endpoint": endpoint,
                        "limit": limit,
                        "window": window
                    })
                    return Response(
                        content=json.dumps({
                            "error": "Rate limit exceeded",
                            "retry_after": window
                        }),
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        media_type="application/json",
                        headers={"Retry-After": str(window)}
                    )
        
        # 全局速率限制
        global_limit = 1000  # 每小时1000次请求
        global_window = 3600  # 1小时
        if not self.rate_limiter.is_allowed(f"global_{client_ip}", global_limit, global_window):
            return Response(
                content=json.dumps({"error": "Global rate limit exceeded"}),
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                media_type="application/json"
            )
        
        return None
    
    def _is_suspicious_path(self, path: str) -> bool:
        """检查是否为可疑路径"""
        suspicious_paths = [
            "/admin", "/wp-admin", "/phpmyadmin", "/.env", "/config",
            "/backup", "/test", "/debug", "/.git", "/swagger.json"
        ]
        
        path_lower = path.lower()
        
        # 检查明确的可疑路径
        for suspicious in suspicious_paths:
            if suspicious in path_lower:
                return True
        
        # 检查路径遍历
        if "../" in path or "..%2f" in path_lower:
            return True
        
        # 检查文件扩展名
        suspicious_extensions = [".php", ".asp", ".jsp", ".cgi", ".pl"]
        for ext in suspicious_extensions:
            if path_lower.endswith(ext):
                return True
        
        return False
    
    def _add_security_headers(self, response: Response):
        """添加安全响应头"""
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        # 移除可能泄露信息的头部
        headers_to_remove = ["Server", "X-Powered-By"]
        for header in headers_to_remove:
            if header in response.headers:
                del response.headers[header]
    
    async def _log_request(self, request: Request, response: Response, client_ip: str, process_time: float):
        """记录请求日志"""
        # 只记录重要的请求或异常情况
        should_log = (
            response.status_code >= 400 or  # 错误请求
            process_time > 2.0 or  # 慢请求
            request.url.path in self.sensitive_endpoints  # 敏感端点
        )
        
        if should_log:
            logger.info(
                f"{request.method} {request.url.path}",
                client_ip=client_ip,
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                process_time=process_time,
                user_agent=request.headers.get("user-agent", "")[:100],
                referer=request.headers.get("referer", "")[:100]
            )


class CSRFProtection:
    """CSRF保护"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def generate_token(self, session_id: str) -> str:
        """生成CSRF令牌"""
        import hmac
        import hashlib
        import base64
        
        timestamp = str(int(time.time()))
        message = f"{session_id}:{timestamp}"
        signature = hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        token = base64.b64encode(f"{message}:{signature}".encode()).decode()
        return token
    
    def validate_token(self, token: str, session_id: str, max_age: int = 3600) -> bool:
        """验证CSRF令牌"""
        try:
            import hmac
            import hashlib
            import base64
            
            decoded = base64.b64decode(token.encode()).decode()
            parts = decoded.split(":")
            if len(parts) != 3:
                return False
            
            token_session_id, timestamp, signature = parts
            
            # 检查会话ID
            if token_session_id != session_id:
                return False
            
            # 检查时间戳
            if int(time.time()) - int(timestamp) > max_age:
                return False
            
            # 验证签名
            message = f"{token_session_id}:{timestamp}"
            expected_signature = hmac.new(
                self.secret_key.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception:
            return False


# 全局CSRF保护实例
csrf_protection = CSRFProtection(settings.SECRET_KEY)
