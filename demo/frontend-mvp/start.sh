#!/bin/bash

# 知深学习导师 MVP 前端演示应用启动脚本
# 作者: AI Assistant
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}  知深学习导师 MVP 前端演示应用${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_error() {
    print_message "$1" "$RED"
}

print_info() {
    print_message "$1" "$BLUE"
}

# 检查Node.js版本
check_node_version() {
    print_info "检查Node.js版本..."
    
    if ! command -v node &> /dev/null; then
        print_error "错误: 未找到Node.js，请先安装Node.js 18或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        print_error "错误: Node.js版本过低，当前版本: $NODE_VERSION，需要: $REQUIRED_VERSION 或更高"
        exit 1
    fi
    
    print_success "✓ Node.js版本检查通过: v$NODE_VERSION"
}

# 检查npm版本
check_npm_version() {
    print_info "检查npm版本..."
    
    if ! command -v npm &> /dev/null; then
        print_error "错误: 未找到npm"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    print_success "✓ npm版本: v$NPM_VERSION"
}

# 安装依赖
install_dependencies() {
    print_info "安装项目依赖..."
    
    if [ ! -f "package.json" ]; then
        print_error "错误: 未找到package.json文件，请确保在正确的目录中运行此脚本"
        exit 1
    fi
    
    # 检查是否已安装依赖
    if [ ! -d "node_modules" ]; then
        print_info "首次运行，正在安装依赖..."
        npm install
    else
        print_info "检查依赖更新..."
        npm install
    fi
    
    print_success "✓ 依赖安装完成"
}

# 运行类型检查
run_type_check() {
    print_info "运行TypeScript类型检查..."
    
    if npm run type-check; then
        print_success "✓ TypeScript类型检查通过"
    else
        print_warning "⚠ TypeScript类型检查发现问题，但不影响运行"
    fi
}

# 运行代码检查
run_lint() {
    print_info "运行代码规范检查..."
    
    if npm run lint; then
        print_success "✓ 代码规范检查通过"
    else
        print_warning "⚠ 代码规范检查发现问题，但不影响运行"
    fi
}

# 启动开发服务器
start_dev_server() {
    print_info "启动开发服务器..."
    print_info "应用将在 http://localhost:3001 启动"
    print_info "按 Ctrl+C 停止服务器"
    echo ""
    
    # 启动开发服务器
    npm run dev
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h          显示此帮助信息"
    echo "  --skip-checks       跳过类型检查和代码规范检查"
    echo "  --install-only      仅安装依赖，不启动服务器"
    echo "  --build             构建生产版本"
    echo "  --preview           预览生产版本"
    echo ""
    echo "示例:"
    echo "  $0                  # 完整启动流程"
    echo "  $0 --skip-checks    # 跳过检查直接启动"
    echo "  $0 --build          # 构建生产版本"
    echo ""
}

# 构建生产版本
build_production() {
    print_info "构建生产版本..."
    
    npm run build
    
    print_success "✓ 生产版本构建完成"
    print_info "构建产物位于 dist/ 目录"
}

# 预览生产版本
preview_production() {
    print_info "预览生产版本..."
    print_info "预览服务器将在 http://localhost:4173 启动"
    
    npm run preview
}

# 主函数
main() {
    print_header
    
    # 解析命令行参数
    SKIP_CHECKS=false
    INSTALL_ONLY=false
    BUILD_MODE=false
    PREVIEW_MODE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --skip-checks)
                SKIP_CHECKS=true
                shift
                ;;
            --install-only)
                INSTALL_ONLY=true
                shift
                ;;
            --build)
                BUILD_MODE=true
                shift
                ;;
            --preview)
                PREVIEW_MODE=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行检查
    check_node_version
    check_npm_version
    
    # 安装依赖
    install_dependencies
    
    # 根据模式执行不同操作
    if [ "$BUILD_MODE" = true ]; then
        if [ "$SKIP_CHECKS" = false ]; then
            run_type_check
            run_lint
        fi
        build_production
        exit 0
    fi
    
    if [ "$PREVIEW_MODE" = true ]; then
        preview_production
        exit 0
    fi
    
    if [ "$INSTALL_ONLY" = true ]; then
        print_success "✓ 依赖安装完成，可以运行 npm run dev 启动开发服务器"
        exit 0
    fi
    
    # 运行检查（如果未跳过）
    if [ "$SKIP_CHECKS" = false ]; then
        run_type_check
        run_lint
    fi
    
    # 启动开发服务器
    start_dev_server
}

# 捕获中断信号
trap 'print_info "\n正在停止服务器..."; exit 0' INT

# 运行主函数
main "$@"
