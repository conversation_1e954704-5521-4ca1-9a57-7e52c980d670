#!/usr/bin/env node

// 简单的登录测试
import axios from 'axios';

const BASE_URL = 'http://localhost:8000/api/v1';

async function testLogin() {
  console.log('🔐 测试登录功能...\n');

  try {
    // 1. 测试登录
    console.log('1. 使用默认超级用户登录...');
    
    const formData = new FormData();
    formData.append('username', '<EMAIL>');
    formData.append('password', 'changethis');

    const loginResponse = await fetch(`${BASE_URL}/login/access-token`, {
      method: 'POST',
      body: formData,
    });

    if (!loginResponse.ok) {
      const error = await loginResponse.json();
      throw new Error(`登录失败: ${error.detail || loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    console.log('✅ 登录成功!');
    console.log('   Token类型:', loginData.token_type);
    console.log('   Token长度:', loginData.access_token.length);
    console.log('');

    // 2. 测试获取用户信息
    console.log('2. 获取当前用户信息...');
    const userResponse = await fetch(`${BASE_URL}/users/me`, {
      headers: {
        'Authorization': `Bearer ${loginData.access_token}`
      }
    });

    if (!userResponse.ok) {
      throw new Error(`获取用户信息失败: ${userResponse.statusText}`);
    }

    const userData = await userResponse.json();
    console.log('✅ 用户信息获取成功:');
    console.log('   邮箱:', userData.email);
    console.log('   全名:', userData.full_name || '未设置');
    console.log('   是否激活:', userData.is_active);
    console.log('   是否超级用户:', userData.is_superuser);
    console.log('');

    // 3. 测试创建对话
    console.log('3. 测试创建对话...');
    const conversationData = {
      title: '登录测试对话 - ' + new Date().toLocaleString(),
      status: 'active',
      learning_level: 'beginner'
    };

    const createResponse = await fetch(`${BASE_URL}/conversations/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${loginData.access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(conversationData)
    });

    if (!createResponse.ok) {
      const error = await createResponse.json();
      throw new Error(`创建对话失败: ${error.detail || createResponse.statusText}`);
    }

    const conversationResult = await createResponse.json();
    console.log('✅ 对话创建成功:');
    console.log('   对话ID:', conversationResult.id);
    console.log('   标题:', conversationResult.title);
    console.log('   状态:', conversationResult.status);
    console.log('');

    console.log('🎉 所有测试通过！');
    console.log('\n📋 测试总结:');
    console.log('   ✅ 登录功能正常');
    console.log('   ✅ 用户认证正常');
    console.log('   ✅ 对话创建正常');
    console.log('\n💡 现在可以在前端界面中使用自动登录功能了！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 提示: 请确保后端服务正在运行 (docker-compose up -d)');
    } else if (error.message.includes('401')) {
      console.log('\n💡 提示: 认证失败，请检查用户名和密码');
    } else if (error.message.includes('404')) {
      console.log('\n💡 提示: API端点不存在，请检查URL路径');
    }
  }
}

// 运行测试
testLogin();
