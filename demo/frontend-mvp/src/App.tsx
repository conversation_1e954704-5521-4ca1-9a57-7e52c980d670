import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, Container } from '@chakra-ui/react'
import { ErrorBoundary } from 'react-error-boundary'
import HomePage from './pages/HomePage'
import LearningPage from './pages/LearningPage'
import ChatTestPage from './pages/ChatTestPage'
import ApiTestPage from './pages/ApiTestPage'
import ErrorFallback from './components/ErrorFallback'
import Header from './components/Header'

function App() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Box minH="100vh" bg="gray.50">
        <Header />
        <Container maxW="full" p={0}>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/topic/:topicId" element={<LearningPage />} />
            <Route path="/chat-test" element={<ChatTestPage />} />
            <Route path="/api-test" element={<ApiTestPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Container>
      </Box>
    </ErrorBoundary>
  )
}

export default App
