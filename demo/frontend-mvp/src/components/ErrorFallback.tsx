import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Code,
} from '@chakra-ui/react'
import { FallbackProps } from 'react-error-boundary'

const ErrorFallback = ({ error, resetErrorBoundary }: FallbackProps) => {
  return (
    <Container maxW="2xl" py={10}>
      <Box
        bg="white"
        border="1px solid"
        borderColor="gray.200"
        borderRadius="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle>应用程序出现错误！</AlertTitle>
              <AlertDescription>
                很抱歉，应用程序遇到了一个意外错误。请尝试刷新页面或联系技术支持。
              </AlertDescription>
            </Box>
          </Alert>

          <VStack spacing={4} align="stretch">
            <Heading size="md" color="red.500">
              错误详情
            </Heading>

            <Box>
              <Text fontWeight="semibold" mb={2}>
                错误消息：
              </Text>
              <Code
                display="block"
                whiteSpace="pre-wrap"
                p={3}
                borderRadius="md"
                bg="red.50"
                color="red.800"
              >
                {error.message}
              </Code>
            </Box>
          </VStack>

          <VStack spacing={3}>
            <Button
              colorScheme="blue"
              onClick={resetErrorBoundary}
              size="lg"
              width="full"
            >
              重试
            </Button>

            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              size="md"
              width="full"
            >
              刷新页面
            </Button>
          </VStack>
        </VStack>
      </Box>
    </Container>
  )
}

export default ErrorFallback
