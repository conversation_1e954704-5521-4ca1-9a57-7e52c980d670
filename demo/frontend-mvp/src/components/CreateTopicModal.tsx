import React, { useState } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  HStack,
  Text,
  useToast,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Box,
  Icon,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react'
import { useForm, Controller } from 'react-hook-form'
import { useMutation } from '@tanstack/react-query'
import { FiUpload, FiType, FiX } from 'react-icons/fi'
import { mockTopicService } from '@/services/topicService'
import { CreateTopicForm } from '@/types'
import FileUploadZone from './FileUploadZone'

interface CreateTopicModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

const CreateTopicModal: React.FC<CreateTopicModalProps> = ({
  isO<PERSON>,
  onClose,
  onSuccess,
}) => {
  const toast = useToast()
  const [knowledgeSources, setKnowledgeSources] = useState<
    CreateTopicForm['knowledge_sources']
  >([])

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<CreateTopicForm>()

  const createTopicMutation = useMutation({
    mutationFn: mockTopicService.createTopic,
    onSuccess: () => {
      toast({
        title: '主题创建成功',
        description: '新的学习主题已创建，可以开始学习了！',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      reset()
      setKnowledgeSources([])
      onSuccess()
    },
    onError: (error: any) => {
      toast({
        title: '创建失败',
        description: error.message || '创建主题时发生错误',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    },
  })

  const handleFileUpload = (files: File[]) => {
    const newSources = files.map((file) => ({
      type: 'file' as const,
      name: file.name,
      content: '',
      file,
    }))
    setKnowledgeSources((prev) => [...prev, ...newSources])
  }

  const handleTextAdd = (name: string, content: string) => {
    if (!name.trim() || !content.trim()) {
      toast({
        title: '输入不完整',
        description: '请填写文本名称和内容',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    const newSource = {
      type: 'text' as const,
      name: name.trim(),
      content: content.trim(),
    }
    setKnowledgeSources((prev) => [...prev, newSource])
  }

  const removeKnowledgeSource = (index: number) => {
    setKnowledgeSources((prev) => prev.filter((_, i) => i !== index))
  }

  const onSubmit = (data: CreateTopicForm) => {
    if (knowledgeSources.length === 0) {
      toast({
        title: '缺少知识源',
        description: '请至少添加一个文件或文本作为学习材料',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    createTopicMutation.mutate({
      ...data,
      knowledge_sources: knowledgeSources,
    })
  }

  const handleClose = () => {
    if (!createTopicMutation.isPending) {
      reset()
      setKnowledgeSources([])
      onClose()
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl" closeOnOverlayClick={false}>
      <ModalOverlay />
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>创建新的学习主题</ModalHeader>
          <ModalCloseButton isDisabled={createTopicMutation.isPending} />
          
          <ModalBody>
            <VStack spacing={6} align="stretch">
              {/* 基本信息 */}
              <VStack spacing={4} align="stretch">
                <FormControl isRequired isInvalid={!!errors.title}>
                  <FormLabel>主题标题</FormLabel>
                  <Input
                    {...register('title', {
                      required: '请输入主题标题',
                      minLength: {
                        value: 2,
                        message: '标题至少需要2个字符',
                      },
                    })}
                    placeholder="例如：React Hooks 深度学习"
                  />
                  {errors.title && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {errors.title.message}
                    </Text>
                  )}
                </FormControl>

                <FormControl>
                  <FormLabel>主题描述（可选）</FormLabel>
                  <Textarea
                    {...register('description')}
                    placeholder="简要描述这个学习主题的内容和目标..."
                    rows={3}
                  />
                </FormControl>
              </VStack>

              {/* 知识源添加 */}
              <Box>
                <Text fontWeight="semibold" mb={4}>
                  添加学习材料
                </Text>
                
                <Tabs variant="enclosed">
                  <TabList>
                    <Tab>
                      <Icon as={FiUpload} mr={2} />
                      文件上传
                    </Tab>
                    <Tab>
                      <Icon as={FiType} mr={2} />
                      文本粘贴
                    </Tab>
                  </TabList>

                  <TabPanels>
                    <TabPanel px={0}>
                      <FileUploadZone
                        onFilesSelected={handleFileUpload}
                        acceptedFileTypes={['.txt', '.md']}
                        maxFiles={5}
                      />
                    </TabPanel>
                    
                    <TabPanel px={0}>
                      <TextInputPanel onAdd={handleTextAdd} />
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </Box>

              {/* 已添加的知识源列表 */}
              {knowledgeSources.length > 0 && (
                <Box>
                  <Text fontWeight="semibold" mb={3}>
                    已添加的学习材料 ({knowledgeSources.length})
                  </Text>
                  <VStack spacing={2} align="stretch">
                    {knowledgeSources.map((source, index) => (
                      <HStack
                        key={index}
                        p={3}
                        bg="gray.50"
                        _dark={{ bg: 'gray.700' }}
                        borderRadius="md"
                        justify="space-between"
                      >
                        <HStack spacing={2}>
                          <Icon
                            as={source.type === 'file' ? FiUpload : FiType}
                            color="brand.500"
                          />
                          <Text fontSize="sm" fontWeight="medium">
                            {source.name}
                          </Text>
                          {source.type === 'text' && (
                            <Text fontSize="xs" color="gray.500">
                              ({source.content.length} 字符)
                            </Text>
                          )}
                        </HStack>
                        <Button
                          size="sm"
                          variant="ghost"
                          colorScheme="red"
                          onClick={() => removeKnowledgeSource(index)}
                        >
                          <Icon as={FiX} />
                        </Button>
                      </HStack>
                    ))}
                  </VStack>
                </Box>
              )}

              {knowledgeSources.length === 0 && (
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <AlertDescription>
                    请至少添加一个文件或文本作为学习材料
                  </AlertDescription>
                </Alert>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button
              variant="ghost"
              mr={3}
              onClick={handleClose}
              isDisabled={createTopicMutation.isPending}
            >
              取消
            </Button>
            <Button
              type="submit"
              colorScheme="brand"
              isLoading={createTopicMutation.isPending}
              loadingText="创建中..."
              isDisabled={knowledgeSources.length === 0}
            >
              创建主题
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  )
}

// 文本输入面板组件
const TextInputPanel: React.FC<{ onAdd: (name: string, content: string) => void }> = ({
  onAdd,
}) => {
  const [name, setName] = useState('')
  const [content, setContent] = useState('')

  const handleAdd = () => {
    onAdd(name, content)
    setName('')
    setContent('')
  }

  return (
    <VStack spacing={4} align="stretch">
      <FormControl>
        <FormLabel>文本名称</FormLabel>
        <Input
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="例如：学习笔记、文章摘录等"
        />
      </FormControl>
      
      <FormControl>
        <FormLabel>文本内容</FormLabel>
        <Textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="粘贴或输入要学习的文本内容..."
          rows={8}
        />
      </FormControl>
      
      <Button
        onClick={handleAdd}
        colorScheme="brand"
        variant="outline"
        isDisabled={!name.trim() || !content.trim()}
      >
        添加文本
      </Button>
    </VStack>
  )
}

export default CreateTopicModal
