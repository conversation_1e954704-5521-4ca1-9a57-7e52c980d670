import React from 'react'
import {
  Box,
  Container,
  Flex,
  Heading,
  Text,
  Button,
  HStack,
  Badge,
  Icon,
  useColorModeValue,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
} from '@chakra-ui/react'
import { FiArrowLeft, FiBook, FiClock, FiMessageCircle, FiMoreVertical, FiPause, FiPlay, FiCheck } from 'react-icons/fi'
import { Topic } from '@/types'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface TopicHeaderProps {
  topic: Topic
  onBack: () => void
}

const TopicHeader: React.FC<TopicHeaderProps> = ({ topic, onBack }) => {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  const getStatusColor = (status: Topic['status']) => {
    switch (status) {
      case 'active':
        return 'green'
      case 'completed':
        return 'blue'
      case 'paused':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  const getStatusText = (status: Topic['status']) => {
    switch (status) {
      case 'active':
        return '学习中'
      case 'completed':
        return '已完成'
      case 'paused':
        return '已暂停'
      default:
        return '未知'
    }
  }

  const formatLastActivity = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN,
      })
    } catch {
      return '未知时间'
    }
  }

  const handleStatusChange = (newStatus: Topic['status']) => {
    // TODO: 实现状态更改逻辑
    console.log('Change status to:', newStatus)
  }

  return (
    <Box
      bg={bgColor}
      borderBottom="1px solid"
      borderColor={borderColor}
      position="sticky"
      top="64px" // 考虑主头部的高度
      zIndex={100}
      boxShadow="sm"
    >
      <Container maxW="full" px={6}>
        <Flex h={20} alignItems="center" justifyContent="space-between">
          {/* 左侧：返回按钮和主题信息 */}
          <HStack spacing={4} flex={1}>
            <Button
              leftIcon={<FiArrowLeft />}
              variant="ghost"
              size="sm"
              onClick={onBack}
            >
              返回
            </Button>

            <Box flex={1}>
              <HStack spacing={3} align="center" mb={1}>
                <Heading size="md" noOfLines={1}>
                  {topic.title}
                </Heading>
                <Badge
                  colorScheme={getStatusColor(topic.status)}
                  variant="subtle"
                  fontSize="xs"
                  px={2}
                  py={1}
                  borderRadius="md"
                >
                  {getStatusText(topic.status)}
                </Badge>
              </HStack>
              
              {topic.description && (
                <Text
                  fontSize="sm"
                  color="gray.600"
                  noOfLines={1}
                  maxW="500px"
                >
                  {topic.description}
                </Text>
              )}
            </Box>
          </HStack>

          {/* 右侧：统计信息和操作按钮 */}
          <HStack spacing={6}>
            {/* 统计信息 */}
            <HStack spacing={4} display={{ base: 'none', md: 'flex' }}>
              <Tooltip label="对话次数" placement="bottom" hasArrow>
                <HStack spacing={1}>
                  <Icon as={FiMessageCircle} size="16px" color="gray.500" />
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    {topic.conversation_count}
                  </Text>
                </HStack>
              </Tooltip>
              
              <Tooltip label="知识源数量" placement="bottom" hasArrow>
                <HStack spacing={1}>
                  <Icon as={FiBook} size="16px" color="gray.500" />
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    {topic.knowledge_sources.length}
                  </Text>
                </HStack>
              </Tooltip>

              <Tooltip label="最后活动时间" placement="bottom" hasArrow>
                <HStack spacing={1}>
                  <Icon as={FiClock} size="16px" color="gray.500" />
                  <Text fontSize="sm" color="gray.500">
                    {formatLastActivity(topic.last_activity)}
                  </Text>
                </HStack>
              </Tooltip>
            </HStack>

            {/* 操作菜单 */}
            <Menu>
              <MenuButton
                as={IconButton}
                icon={<FiMoreVertical />}
                variant="ghost"
                size="sm"
                aria-label="更多操作"
              />
              <MenuList>
                {topic.status === 'active' && (
                  <>
                    <MenuItem
                      icon={<FiPause />}
                      onClick={() => handleStatusChange('paused')}
                    >
                      暂停学习
                    </MenuItem>
                    <MenuItem
                      icon={<FiCheck />}
                      onClick={() => handleStatusChange('completed')}
                    >
                      标记完成
                    </MenuItem>
                  </>
                )}
                
                {topic.status === 'paused' && (
                  <>
                    <MenuItem
                      icon={<FiPlay />}
                      onClick={() => handleStatusChange('active')}
                    >
                      继续学习
                    </MenuItem>
                    <MenuItem
                      icon={<FiCheck />}
                      onClick={() => handleStatusChange('completed')}
                    >
                      标记完成
                    </MenuItem>
                  </>
                )}
                
                {topic.status === 'completed' && (
                  <MenuItem
                    icon={<FiPlay />}
                    onClick={() => handleStatusChange('active')}
                  >
                    重新开始
                  </MenuItem>
                )}
              </MenuList>
            </Menu>
          </HStack>
        </Flex>
      </Container>
    </Box>
  )
}

export default TopicHeader
