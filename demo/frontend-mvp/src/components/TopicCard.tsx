import React from 'react'
import {
  Box,
  Card,
  CardBody,
  Heading,
  Text,
  Badge,
  VStack,
  HStack,
  Icon,
  useColorModeValue,
  Tooltip,
  Progress,
} from '@chakra-ui/react'
import { FiBook, FiClock, FiMessageCircle, FiFile, FiType } from 'react-icons/fi'
import { Topic } from '@/types'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface TopicCardProps {
  topic: Topic
  onClick: () => void
}

const TopicCard: React.FC<TopicCardProps> = ({ topic, onClick }) => {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')

  const getStatusColor = (status: Topic['status']) => {
    switch (status) {
      case 'active':
        return 'green'
      case 'completed':
        return 'blue'
      case 'paused':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  const getStatusText = (status: Topic['status']) => {
    switch (status) {
      case 'active':
        return '学习中'
      case 'completed':
        return '已完成'
      case 'paused':
        return '已暂停'
      default:
        return '未知'
    }
  }

  const formatLastActivity = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN,
      })
    } catch {
      return '未知时间'
    }
  }

  const getProgressValue = () => {
    if (topic.status === 'completed') return 100
    if (topic.status === 'paused') return 50
    return Math.min((topic.conversation_count / 20) * 100, 90) // 假设20次对话为完成
  }

  return (
    <Card
      bg={bgColor}
      border="1px solid"
      borderColor={borderColor}
      cursor="pointer"
      onClick={onClick}
      transition="all 0.2s"
      _hover={{
        bg: hoverBg,
        transform: 'translateY(-2px)',
        boxShadow: 'lg',
        borderColor: 'brand.300',
      }}
      height="fit-content"
    >
      <CardBody p={6}>
        <VStack align="stretch" spacing={4}>
          {/* 标题和状态 */}
          <HStack justify="space-between" align="start">
            <VStack align="start" spacing={1} flex={1}>
              <Heading size="md" noOfLines={2} lineHeight="1.3">
                {topic.title}
              </Heading>
              {topic.description && (
                <Text
                  fontSize="sm"
                  color="gray.600"
                  noOfLines={2}
                  lineHeight="1.4"
                >
                  {topic.description}
                </Text>
              )}
            </VStack>
            <Badge
              colorScheme={getStatusColor(topic.status)}
              variant="subtle"
              fontSize="xs"
              px={2}
              py={1}
              borderRadius="md"
            >
              {getStatusText(topic.status)}
            </Badge>
          </HStack>

          {/* 进度条 */}
          <Box>
            <HStack justify="space-between" mb={1}>
              <Text fontSize="xs" color="gray.500">
                学习进度
              </Text>
              <Text fontSize="xs" color="gray.500">
                {Math.round(getProgressValue())}%
              </Text>
            </HStack>
            <Progress
              value={getProgressValue()}
              size="sm"
              colorScheme={getStatusColor(topic.status)}
              borderRadius="full"
            />
          </Box>

          {/* 知识源信息 */}
          <Box>
            <Text fontSize="xs" color="gray.500" mb={2}>
              知识源
            </Text>
            <HStack spacing={2} flexWrap="wrap">
              {topic.knowledge_sources.slice(0, 3).map((source, index) => (
                <Tooltip
                  key={source.id}
                  label={source.name}
                  placement="top"
                  hasArrow
                >
                  <HStack
                    spacing={1}
                    bg="gray.100"
                    _dark={{ bg: 'gray.700' }}
                    px={2}
                    py={1}
                    borderRadius="md"
                    fontSize="xs"
                  >
                    <Icon
                      as={source.type === 'file' ? FiFile : FiType}
                      size="12px"
                    />
                    <Text noOfLines={1} maxW="80px">
                      {source.name}
                    </Text>
                  </HStack>
                </Tooltip>
              ))}
              {topic.knowledge_sources.length > 3 && (
                <Badge variant="outline" fontSize="xs">
                  +{topic.knowledge_sources.length - 3}
                </Badge>
              )}
            </HStack>
          </Box>

          {/* 统计信息 */}
          <HStack justify="space-between" pt={2} borderTop="1px solid" borderColor={borderColor}>
            <HStack spacing={4}>
              <Tooltip label="对话次数" placement="top" hasArrow>
                <HStack spacing={1}>
                  <Icon as={FiMessageCircle} size="14px" color="gray.500" />
                  <Text fontSize="sm" color="gray.600">
                    {topic.conversation_count}
                  </Text>
                </HStack>
              </Tooltip>
              
              <Tooltip label="知识源数量" placement="top" hasArrow>
                <HStack spacing={1}>
                  <Icon as={FiBook} size="14px" color="gray.500" />
                  <Text fontSize="sm" color="gray.600">
                    {topic.knowledge_sources.length}
                  </Text>
                </HStack>
              </Tooltip>
            </HStack>

            <Tooltip label="最后活动时间" placement="top" hasArrow>
              <HStack spacing={1}>
                <Icon as={FiClock} size="14px" color="gray.500" />
                <Text fontSize="xs" color="gray.500">
                  {formatLastActivity(topic.last_activity)}
                </Text>
              </HStack>
            </Tooltip>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default TopicCard
