import React, { useCallback, useState, useRef } from 'react'
import {
  Box,
  Text,
  VStack,
  HStack,
  Icon,
  Button,
  useColorModeValue,
  Progress,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react'
import { FiUpload, FiFile, FiX } from 'react-icons/fi'

interface FileUploadZoneProps {
  onFilesSelected: (files: File[]) => void
  acceptedFileTypes?: string[]
  maxFiles?: number
  maxFileSize?: number // in bytes
}

const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFilesSelected,
  acceptedFileTypes = ['.txt', '.md'],
  maxFiles = 5,
  maxFileSize = 10 * 1024 * 1024, // 10MB
}) => {
  const [uploadError, setUploadError] = useState<string>('')
  const [isDragActive, setIsDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const bgColor = useColorModeValue('gray.50', 'gray.700')
  const borderColor = useColorModeValue('gray.300', 'gray.600')
  const hoverBgColor = useColorModeValue('gray.100', 'gray.600')

  const validateFiles = (files: FileList): File[] => {
    const validFiles: File[] = []
    const errors: string[] = []

    Array.from(files).forEach((file) => {
      // 检查文件类型
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (!acceptedFileTypes.includes(fileExtension)) {
        errors.push(`${file.name}: 不支持的文件类型`)
        return
      }

      // 检查文件大小
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: 文件过大`)
        return
      }

      validFiles.push(file)
    })

    // 检查文件数量
    if (validFiles.length > maxFiles) {
      setUploadError(`最多只能上传 ${maxFiles} 个文件`)
      return []
    }

    if (errors.length > 0) {
      setUploadError(errors.join(', '))
      return []
    }

    setUploadError('')
    return validFiles
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files && files.length > 0) {
      const validFiles = validateFiles(files)
      if (validFiles.length > 0) {
        onFilesSelected(validFiles)
      }
    }
    // 重置input值，允许重复选择同一文件
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragActive(true)
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragActive(false)
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragActive(false)

    const files = event.dataTransfer.files
    if (files && files.length > 0) {
      const validFiles = validateFiles(files)
      if (validFiles.length > 0) {
        onFilesSelected(validFiles)
      }
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <VStack spacing={4} align="stretch">
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedFileTypes.join(',')}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      <Box
        p={8}
        border="2px dashed"
        borderColor={isDragActive ? 'brand.400' : borderColor}
        borderRadius="lg"
        bg={isDragActive ? 'brand.50' : bgColor}
        _hover={{ bg: hoverBgColor, borderColor: 'brand.300' }}
        cursor="pointer"
        transition="all 0.2s"
        textAlign="center"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <VStack spacing={4}>
          <Icon
            as={FiUpload}
            size="48px"
            color={isDragActive ? 'brand.500' : 'gray.400'}
          />

          {isDragActive ? (
            <Text color="brand.600" fontWeight="medium">
              松开鼠标上传文件
            </Text>
          ) : (
            <VStack spacing={2}>
              <Text fontWeight="medium" color="gray.700" _dark={{ color: 'gray.300' }}>
                拖拽文件到这里，或点击选择文件
              </Text>
              <Text fontSize="sm" color="gray.500">
                支持 {acceptedFileTypes.join(', ')} 格式
              </Text>
              <Text fontSize="sm" color="gray.500">
                最多 {maxFiles} 个文件，每个文件最大 {formatFileSize(maxFileSize)}
              </Text>
            </VStack>
          )}
        </VStack>
      </Box>

      {uploadError && (
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          <AlertDescription>{uploadError}</AlertDescription>
        </Alert>
      )}

      <Button
        leftIcon={<FiFile />}
        variant="outline"
        onClick={handleClick}
      >
        选择文件
      </Button>
    </VStack>
  )
}

export default FileUploadZone
