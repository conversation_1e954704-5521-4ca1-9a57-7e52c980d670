import React, { useEffect, useRef } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Avatar,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
  Badge,
  Divider,
  Flex,
} from '@chakra-ui/react'
import { <PERSON><PERSON>ser, <PERSON><PERSON>pu, FiClock } from 'react-icons/fi'
import { useQuery } from '@tanstack/react-query'
import { ConversationSummary } from '@/types'
import { mockChatService } from '@/services/chatService'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface SummaryPanelProps {
  topicId: string
  onSummaryClick: (messageId: string) => void
  selectedMessageId: string | null
}

const SummaryPanel: React.FC<SummaryPanelProps> = ({
  topicId,
  onSummaryClick,
  selectedMessageId,
}) => {
  const summaryRefs = useRef<{ [key: string]: HTMLDivElement }>({})
  
  const bgColor = useColorModeValue('gray.50', 'gray.900')
  const cardBgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 获取摘要列表
  const {
    data: summaries = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['summaries', topicId],
    queryFn: () => mockChatService.getSummaries(topicId),
    refetchInterval: 5000, // 每5秒刷新一次，获取新的摘要
  })

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN,
      })
    } catch {
      return '刚刚'
    }
  }

  if (isLoading) {
    return (
      <Flex h="full" align="center" justify="center" bg={bgColor}>
        <VStack spacing={4}>
          <Spinner size="lg" color="brand.500" />
          <Text color="gray.500">加载摘要...</Text>
        </VStack>
      </Flex>
    )
  }

  if (error) {
    return (
      <Box h="full" bg={bgColor} p={6}>
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          <AlertDescription>加载摘要失败</AlertDescription>
        </Alert>
      </Box>
    )
  }

  return (
    <Box h="full" bg={bgColor}>
      {/* 头部 */}
      <Box
        p={6}
        borderBottom="1px solid"
        borderColor={borderColor}
        bg={cardBgColor}
      >
        <VStack spacing={2} align="start">
          <Heading size="md" color="brand.500">
            对话摘要
          </Heading>
          <Text fontSize="sm" color="gray.600">
            点击摘要可跳转到对应对话
          </Text>
          {summaries.length > 0 && (
            <Badge colorScheme="brand" variant="subtle" fontSize="xs">
              {summaries.length} 个摘要
            </Badge>
          )}
        </VStack>
      </Box>

      {/* 摘要列表 */}
      <Box flex={1} overflowY="auto" p={4}>
        {summaries.length === 0 ? (
          <Box textAlign="center" py={12}>
            <VStack spacing={4}>
              <FiCpu size={48} color="gray.400" />
              <Text color="gray.500">
                还没有对话摘要
              </Text>
              <Text color="gray.400" fontSize="sm" textAlign="center">
                开始对话后，AI会自动生成摘要
              </Text>
            </VStack>
          </Box>
        ) : (
          <VStack spacing={4} align="stretch">
            {summaries.map((summary, index) => (
              <SummaryCard
                key={summary.id}
                summary={summary}
                index={index + 1}
                onClick={() => onSummaryClick(summary.message_range.start_message_id)}
                isRelated={
                  selectedMessageId === summary.message_range.start_message_id ||
                  selectedMessageId === summary.message_range.end_message_id
                }
                ref={(el) => {
                  if (el) summaryRefs.current[summary.id] = el
                }}
              />
            ))}
          </VStack>
        )}
      </Box>
    </Box>
  )
}

// 摘要卡片组件
interface SummaryCardProps {
  summary: ConversationSummary
  index: number
  onClick: () => void
  isRelated?: boolean
}

const SummaryCard = React.forwardRef<HTMLDivElement, SummaryCardProps>(
  ({ summary, index, onClick, isRelated }, ref) => {
    const cardBgColor = useColorModeValue('white', 'gray.800')
    const borderColor = useColorModeValue('gray.200', 'gray.700')
    const hoverBgColor = useColorModeValue('gray.50', 'gray.700')

    const formatTime = (dateString: string) => {
      try {
        return formatDistanceToNow(new Date(dateString), {
          addSuffix: true,
          locale: zhCN,
        })
      } catch {
        return '刚刚'
      }
    }

    return (
      <Box
        ref={ref}
        bg={cardBgColor}
        border="1px solid"
        borderColor={isRelated ? 'brand.300' : borderColor}
        borderRadius="lg"
        p={4}
        cursor="pointer"
        onClick={onClick}
        transition="all 0.2s"
        _hover={{
          bg: hoverBgColor,
          borderColor: 'brand.400',
          transform: 'translateY(-1px)',
          boxShadow: 'md',
        }}
        boxShadow={isRelated ? 'md' : 'sm'}
      >
        <VStack spacing={3} align="stretch">
          {/* 头部信息 */}
          <HStack justify="space-between" align="start">
            <Badge colorScheme="brand" variant="outline" fontSize="xs">
              对话 {index}
            </Badge>
            <HStack spacing={1}>
              <FiClock size="12px" color="gray.500" />
              <Text fontSize="xs" color="gray.500">
                {formatTime(summary.created_at)}
              </Text>
            </HStack>
          </HStack>

          {/* 用户摘要 */}
          <HStack spacing={3} align="start">
            <Avatar size="xs" icon={<FiUser />} bg="gray.500" color="white" />
            <Box flex={1}>
              <Text fontSize="sm" color="gray.600" mb={1} fontWeight="medium">
                你：
              </Text>
              <Text fontSize="sm" lineHeight="1.4" noOfLines={3}>
                {summary.user_summary}
              </Text>
            </Box>
          </HStack>

          <Divider />

          {/* AI摘要 */}
          <HStack spacing={3} align="start">
            <Avatar size="xs" icon={<FiCpu />} bg="brand.500" color="white" />
            <Box flex={1}>
              <Text fontSize="sm" color="gray.600" mb={1} fontWeight="medium">
                AI导师：
              </Text>
              <Text fontSize="sm" lineHeight="1.4" noOfLines={4}>
                {summary.assistant_summary}
              </Text>
            </Box>
          </HStack>
        </VStack>
      </Box>
    )
  }
)

SummaryCard.displayName = 'SummaryCard'

export default SummaryPanel
