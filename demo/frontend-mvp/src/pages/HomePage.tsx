import { useState } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  But<PERSON>,
  V<PERSON>tack,
  Spinner,
  <PERSON><PERSON>,
  AlertIcon,
  AlertDescription,
  Flex,
} from '@chakra-ui/react'
import { AddIcon } from '@chakra-ui/icons'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { mockTopicService } from '@/services/topicService'
import { TopicFilter, Topic } from '@/types'

const HomePage = () => {
  const navigate = useNavigate()
  const [filter] = useState<TopicFilter>({
    sort_by: 'updated_at',
    sort_order: 'desc',
  })

  // 获取主题列表
  const {
    data: topicsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['topics', filter],
    queryFn: () => mockTopicService.getTopics(filter),
  })

  const handleTopicClick = (topic: Topic) => {
    navigate(`/topic/${topic.id}`)
  }

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* 页面标题 */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
          <VStack align="start" spacing={2}>
            <Heading size="xl" color="brand.500">
              我的学习主题
            </Heading>
            <Text color="gray.600" fontSize="lg">
              开始一个新的学习主题，或继续之前的学习进度
            </Text>
          </VStack>

          <Button
            leftIcon={<AddIcon />}
            colorScheme="brand"
            size="lg"
            onClick={() => alert('创建主题功能开发中...')}
          >
            创建新主题
          </Button>
        </Flex>

        {/* 主题列表 */}
        <Box>
          {isLoading && (
            <Flex justify="center" py={12}>
              <VStack spacing={4}>
                <Spinner size="xl" color="brand.500" />
                <Text color="gray.500">加载主题列表...</Text>
              </VStack>
            </Flex>
          )}

          {error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              <AlertDescription>
                加载主题列表失败，请稍后重试。
              </AlertDescription>
            </Alert>
          )}

          {topicsData && topicsData.items.length === 0 && (
            <Box textAlign="center" py={12} bg="white" borderRadius="lg">
              <VStack spacing={4}>
                <Heading size="md" color="gray.500">
                  还没有学习主题
                </Heading>
                <Text color="gray.400">
                  创建你的第一个学习主题，开始AI辅助学习之旅
                </Text>
                <Button
                  leftIcon={<AddIcon />}
                  colorScheme="brand"
                  onClick={() => alert('创建主题功能开发中...')}
                >
                  创建新主题
                </Button>
              </VStack>
            </Box>
          )}

          {topicsData && topicsData.items.length > 0 && (
            <VStack spacing={4}>
              {topicsData.items.map((topic) => (
                <Box
                  key={topic.id}
                  p={6}
                  bg="white"
                  borderRadius="lg"
                  boxShadow="sm"
                  cursor="pointer"
                  onClick={() => handleTopicClick(topic)}
                  _hover={{ boxShadow: 'md' }}
                >
                  <Heading size="md" mb={2}>
                    {topic.title}
                  </Heading>
                  {topic.description && (
                    <Text color="gray.600" mb={2}>
                      {topic.description}
                    </Text>
                  )}
                  <Text fontSize="sm" color="gray.500">
                    {topic.conversation_count} 次对话 • {topic.knowledge_sources.length} 个知识源
                  </Text>
                </Box>
              ))}
            </VStack>
          )}
        </Box>
      </VStack>
    </Container>
  )
}

export default HomePage
