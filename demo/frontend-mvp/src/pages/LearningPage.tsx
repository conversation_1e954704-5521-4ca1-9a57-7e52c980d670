import React from 'react'
import {
  <PERSON>,
  Spinner,
  <PERSON><PERSON>,
  AlertIcon,
  AlertDescription,
  Button,
  VStack,
  Text,
  Container,
  Heading,
} from '@chakra-ui/react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { FiArrowLeft } from 'react-icons/fi'
import { mockTopicService } from '@/services/topicService'

const LearningPage = () => {
  const { topicId } = useParams<{ topicId: string }>()
  const navigate = useNavigate()

  // 获取主题详情
  const {
    data: topic,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['topic', topicId],
    queryFn: () => mockTopicService.getTopic(topicId!),
    enabled: !!topicId,
  })

  if (isLoading) {
    return (
      <Box
        minH="100vh"
        bg="gray.50"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={4}>
          <Spinner size="xl" color="brand.500" />
          <Text color="gray.500">加载学习主题...</Text>
        </VStack>
      </Box>
    )
  }

  if (error || !topic) {
    return (
      <Box
        minH="100vh"
        bg="gray.50"
        display="flex"
        alignItems="center"
        justifyContent="center"
        p={8}
      >
        <VStack spacing={6} maxW="md" textAlign="center">
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <AlertDescription>
              {error ? '加载主题失败' : '主题不存在'}
            </AlertDescription>
          </Alert>

          <Button
            leftIcon={<FiArrowLeft />}
            onClick={() => navigate('/')}
            colorScheme="brand"
          >
            返回主题列表
          </Button>
        </VStack>
      </Box>
    )
  }

  return (
    <Box minH="100vh" bg="gray.50">
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* 主题信息 */}
          <Box bg="white" p={6} borderRadius="lg" boxShadow="sm">
            <Button
              leftIcon={<FiArrowLeft />}
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              mb={4}
            >
              返回主题列表
            </Button>

            <Heading size="lg" mb={2}>
              {topic.title}
            </Heading>
            {topic.description && (
              <Text color="gray.600" mb={4}>
                {topic.description}
              </Text>
            )}
            <Text fontSize="sm" color="gray.500">
              {topic.conversation_count} 次对话 • {topic.knowledge_sources.length} 个知识源
            </Text>
          </Box>

          {/* 学习界面占位 */}
          <Box bg="white" p={8} borderRadius="lg" boxShadow="sm" textAlign="center">
            <VStack spacing={4}>
              <Heading size="md" color="gray.500">
                学习界面开发中...
              </Heading>
              <Text color="gray.400">
                双栏对话界面和摘要功能正在开发中
              </Text>
              <Text fontSize="sm" color="gray.400">
                这里将展示：左侧对话区域 + 右侧实时摘要面板
              </Text>
            </VStack>
          </Box>
        </VStack>
      </Container>
    </Box>
  )
}

export default LearningPage
