import { api } from './api'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in?: number
}

export interface User {
  id: string
  email: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
}

export const authService = {
  // 登录
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    // 使用 form data 格式，因为后端期望的是 OAuth2PasswordRequestForm
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const response = await fetch('/api/v1/login/access-token', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || '登录失败')
    }

    return response.json()
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    return api.get<User>('/users/me')
  },

  // 测试token是否有效
  testToken: async (): Promise<User> => {
    return api.post<User>('/login/test-token', {})
  },

  // 登出
  logout: () => {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  },

  // 获取存储的token
  getToken: (): string | null => {
    return localStorage.getItem('auth_token')
  },

  // 存储token
  setToken: (token: string) => {
    localStorage.setItem('auth_token', token)
  },

  // 获取存储的用户信息
  getUser: (): User | null => {
    const userStr = localStorage.getItem('user_info')
    return userStr ? JSON.parse(userStr) : null
  },

  // 存储用户信息
  setUser: (user: User) => {
    localStorage.setItem('user_info', JSON.stringify(user))
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    return !!authService.getToken()
  },

  // 自动登录（使用默认的超级用户）
  autoLogin: async (): Promise<LoginResponse> => {
    return authService.login({
      username: '<EMAIL>',
      password: 'changethis'
    })
  }
}
