import { api } from './api'
import { Topic, CreateTopicForm, TopicFilter, PaginatedResponse } from '@/types'

export const topicService = {
  // 获取主题列表
  getTopics: async (filter?: TopicFilter): Promise<PaginatedResponse<Topic>> => {
    return api.getPaginated<Topic>('/topics', filter)
  },

  // 获取单个主题详情
  getTopic: async (topicId: string): Promise<Topic> => {
    return api.get<Topic>(`/topics/${topicId}`)
  },

  // 创建新主题
  createTopic: async (data: CreateTopicForm): Promise<Topic> => {
    // 处理文件上传
    const formData = new FormData()
    formData.append('title', data.title)
    if (data.description) {
      formData.append('description', data.description)
    }

    // 处理知识源
    data.knowledge_sources.forEach((source, index) => {
      if (source.type === 'file' && source.file) {
        formData.append(`files`, source.file)
        formData.append(`file_names`, source.name)
      } else if (source.type === 'text') {
        formData.append(`text_sources`, JSON.stringify({
          name: source.name,
          content: source.content,
        }))
      }
    })

    return api.post<Topic>('/topics', formData)
  },

  // 更新主题
  updateTopic: async (topicId: string, data: Partial<Topic>): Promise<Topic> => {
    return api.put<Topic>(`/topics/${topicId}`, data)
  },

  // 删除主题
  deleteTopic: async (topicId: string): Promise<void> => {
    return api.delete<void>(`/topics/${topicId}`)
  },

  // 添加知识源到现有主题
  addKnowledgeSource: async (
    topicId: string,
    source: {
      type: 'file' | 'text'
      name: string
      content?: string
      file?: File
    }
  ): Promise<Topic> => {
    if (source.type === 'file' && source.file) {
      const formData = new FormData()
      formData.append('file', source.file)
      formData.append('name', source.name)
      return api.post<Topic>(`/topics/${topicId}/knowledge-sources/file`, formData)
    } else {
      return api.post<Topic>(`/topics/${topicId}/knowledge-sources/text`, {
        name: source.name,
        content: source.content,
      })
    }
  },

  // 删除知识源
  removeKnowledgeSource: async (
    topicId: string,
    sourceId: string
  ): Promise<Topic> => {
    return api.delete<Topic>(`/topics/${topicId}/knowledge-sources/${sourceId}`)
  },

  // 获取主题统计信息
  getTopicStats: async (topicId: string): Promise<{
    message_count: number
    summary_count: number
    knowledge_source_count: number
    total_tokens: number
    last_activity: string
  }> => {
    return api.get<any>(`/topics/${topicId}/stats`)
  },

  // 搜索主题
  searchTopics: async (query: string): Promise<Topic[]> => {
    return api.get<Topic[]>('/topics/search', { q: query })
  },

  // 获取最近活跃的主题
  getRecentTopics: async (limit: number = 5): Promise<Topic[]> => {
    return api.get<Topic[]>('/topics/recent', { limit })
  },

  // 标记主题为完成
  markTopicCompleted: async (topicId: string): Promise<Topic> => {
    return api.put<Topic>(`/topics/${topicId}/complete`, {})
  },

  // 暂停主题
  pauseTopic: async (topicId: string): Promise<Topic> => {
    return api.put<Topic>(`/topics/${topicId}/pause`, {})
  },

  // 恢复主题
  resumeTopic: async (topicId: string): Promise<Topic> => {
    return api.put<Topic>(`/topics/${topicId}/resume`, {})
  },
}

// Mock数据（用于开发阶段）
export const mockTopicService = {
  getTopics: async (filter?: TopicFilter): Promise<PaginatedResponse<Topic>> => {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockTopics: Topic[] = [
      {
        id: '1',
        title: 'React Hooks 深度学习',
        description: '深入理解React Hooks的原理和最佳实践',
        status: 'active',
        knowledge_sources: [
          {
            id: 'ks1',
            type: 'file',
            name: 'React官方文档.md',
            content: '',
            processed: true,
          }
        ],
        conversation_count: 15,
        last_activity: '2024-01-15T10:30:00Z',
        created_at: '2024-01-10T09:00:00Z',
        updated_at: '2024-01-15T10:30:00Z',
      },
      {
        id: '2',
        title: 'TypeScript 类型系统',
        description: '掌握TypeScript的高级类型特性',
        status: 'completed',
        knowledge_sources: [
          {
            id: 'ks2',
            type: 'text',
            name: '类型系统笔记',
            content: 'TypeScript类型系统的核心概念...',
            processed: true,
          }
        ],
        conversation_count: 8,
        last_activity: '2024-01-12T16:45:00Z',
        created_at: '2024-01-08T14:20:00Z',
        updated_at: '2024-01-12T16:45:00Z',
      },
    ]

    return {
      items: mockTopics,
      total: mockTopics.length,
      page: 1,
      size: 10,
      pages: 1,
    }
  },

  getTopic: async (topicId: string): Promise<Topic> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return {
      id: topicId,
      title: 'React Hooks 深度学习',
      description: '深入理解React Hooks的原理和最佳实践',
      status: 'active',
      knowledge_sources: [
        {
          id: 'ks1',
          type: 'file',
          name: 'React官方文档.md',
          content: '',
          processed: true,
        }
      ],
      conversation_count: 15,
      last_activity: '2024-01-15T10:30:00Z',
      created_at: '2024-01-10T09:00:00Z',
      updated_at: '2024-01-15T10:30:00Z',
    }
  },

  createTopic: async (data: CreateTopicForm): Promise<Topic> => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return {
      id: Date.now().toString(),
      title: data.title,
      description: data.description,
      status: 'active',
      knowledge_sources: data.knowledge_sources.map((source, index) => ({
        id: `ks${index + 1}`,
        type: source.type,
        name: source.name,
        content: source.content,
        processed: false,
      })),
      conversation_count: 0,
      last_activity: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  },
}
