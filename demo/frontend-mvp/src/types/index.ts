// 基础类型定义
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

// 主题相关类型
export interface Topic extends BaseEntity {
  title: string;
  description?: string;
  status: 'active' | 'completed' | 'paused';
  knowledge_sources: KnowledgeSource[];
  conversation_count: number;
  last_activity: string;
}

export interface KnowledgeSource {
  id: string;
  type: 'file' | 'text';
  name: string;
  content: string;
  file_type?: string;
  size?: number;
  processed: boolean;
}

// 对话相关类型
export interface Conversation extends BaseEntity {
  user_id: string;
  title: string;
  topic_id?: string;
  status: 'active' | 'paused' | 'completed' | 'archived';
  learning_level: 'beginner' | 'intermediate' | 'advanced';
  context?: Record<string, any>;
  messages?: Message[];
  summary?: ConversationSummary;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  round_id?: string; // 逻辑回合ID
  metadata?: {
    token_count?: number;
    processing_time?: number;
  };
}

// 摘要相关类型
export interface ConversationSummary {
  id: string;
  conversation_id: string;
  round_id: string;
  user_summary: string;
  assistant_summary: string;
  created_at: string;
  message_range: {
    start_message_id: string;
    end_message_id: string;
  };
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 表单类型
export interface CreateTopicForm {
  title: string;
  description?: string;
  knowledge_sources: {
    type: 'file' | 'text';
    name: string;
    content: string;
    file?: File;
  }[];
}

export interface ChatMessage {
  content: string;
  attachments?: File[];
}

// UI状态类型
export interface UIState {
  isLoading: boolean;
  error?: string;
  selectedTopic?: Topic;
  activeConversation?: Conversation;
}

// 文件上传类型
export interface FileUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

// 搜索和过滤类型
export interface TopicFilter {
  status?: Topic['status'];
  search?: string;
  sort_by?: 'created_at' | 'updated_at' | 'title';
  sort_order?: 'asc' | 'desc';
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'message' | 'summary' | 'status' | 'error';
  data: any;
  timestamp: string;
}
