# 用户故事和测试用例

## 📋 概述

本文档包含了"知深学习导师"MVP前端演示应用的所有用户故事和对应的测试用例。每个用户故事都遵循标准格式："作为[用户角色]，我希望[功能描述]，以便[价值/目标]"。

## 🎯 Epic 1: 主题式学习管理

### US1.1 - 创建学习主题
**用户故事**: 作为学习者，我希望能创建一个新的学习主题，以便开始学习特定内容。

**验收标准**:
- 用户可以点击"创建新主题"按钮
- 用户可以输入主题标题（必填）
- 用户可以输入主题描述（可选）
- 用户必须添加至少一个知识源
- 创建成功后跳转到主题列表

**测试场景**:
1. **正常创建流程**
   - 点击"创建新主题"按钮
   - 输入标题"React Hooks学习"
   - 输入描述"深入学习React Hooks的使用"
   - 上传一个.md文件
   - 点击"创建主题"
   - 验证：主题创建成功，返回主题列表

2. **必填字段验证**
   - 点击"创建新主题"按钮
   - 不输入标题，直接点击"创建主题"
   - 验证：显示"请输入主题标题"错误信息

3. **知识源必填验证**
   - 输入标题但不添加知识源
   - 点击"创建主题"
   - 验证：显示"请至少添加一个文件或文本作为学习材料"提示

### US1.2 - 添加知识源
**用户故事**: 作为学习者，我希望能上传文档或粘贴长文本，以便AI基于这些材料与我对话。

**验收标准**:
- 支持.txt和.md文件上传
- 支持拖拽上传
- 支持长文本粘贴
- 显示文件上传进度
- 可以删除已添加的知识源

**测试场景**:
1. **文件上传**
   - 点击文件上传区域
   - 选择一个.md文件
   - 验证：文件出现在已添加列表中

2. **拖拽上传**
   - 将.txt文件拖拽到上传区域
   - 验证：文件自动添加到列表

3. **文本粘贴**
   - 切换到"文本粘贴"标签
   - 输入文本名称和内容
   - 点击"添加文本"
   - 验证：文本出现在已添加列表中

4. **文件类型验证**
   - 尝试上传.pdf文件
   - 验证：显示"不支持的文件类型"错误

### US1.3 - 浏览学习主题
**用户故事**: 作为学习者，我希望能看到所有历史学习主题，以便选择继续学习。

**验收标准**:
- 以卡片形式展示主题列表
- 显示主题标题、描述、状态
- 显示学习进度和统计信息
- 支持搜索和过滤
- 支持状态排序

**测试场景**:
1. **主题列表展示**
   - 访问主页
   - 验证：显示所有主题卡片
   - 验证：每个卡片显示标题、状态、进度

2. **搜索功能**
   - 在搜索框输入"React"
   - 验证：只显示包含"React"的主题

3. **状态过滤**
   - 选择"学习中"状态过滤
   - 验证：只显示状态为"学习中"的主题

### US1.4 - 继续学习
**用户故事**: 作为学习者，我希望能恢复之前的学习进度，以便继续中断的学习。

**验收标准**:
- 点击主题卡片进入学习页面
- 加载历史对话记录
- 加载历史摘要
- 保持学习状态

**测试场景**:
1. **恢复学习**
   - 点击一个"学习中"状态的主题
   - 验证：进入学习页面
   - 验证：显示历史对话
   - 验证：显示历史摘要

## 🎯 Epic 2: 沉浸式学习体验

### US2.1 - 专注对话界面
**用户故事**: 作为学习者，我希望有一个专注的对话界面，以便与AI进行深度交流。

**验收标准**:
- 左侧显示完整对话历史
- 支持实时消息发送
- 显示消息发送状态
- 支持多行文本输入
- 键盘快捷键支持（Enter发送）

**测试场景**:
1. **发送消息**
   - 在输入框输入"什么是React Hooks？"
   - 按Enter键
   - 验证：消息发送成功
   - 验证：显示AI回复

2. **多行输入**
   - 输入多行文本
   - 按Shift+Enter换行
   - 验证：支持多行输入

3. **发送状态**
   - 发送消息后
   - 验证：显示"AI正在思考..."状态

### US2.2 - 实时摘要展示
**用户故事**: 作为学习者，我希望能实时看到学习要点摘要，以便把握学习进度。

**验收标准**:
- 右侧显示摘要面板
- 摘要以"你/AI"格式展示
- 实时更新新的摘要
- 显示摘要生成时间
- 摘要按时间顺序排列

**测试场景**:
1. **摘要生成**
   - 完成一轮对话
   - 等待2秒
   - 验证：右侧出现新的摘要卡片

2. **摘要格式**
   - 查看摘要卡片
   - 验证：包含"你："和"AI导师："两部分
   - 验证：显示生成时间

### US2.3 - 摘要导航
**用户故事**: 作为学习者，我希望能通过摘要快速回顾对话内容，以便巩固学习成果。

**验收标准**:
- 点击摘要可跳转到对应对话
- 对应消息高亮显示
- 平滑滚动到目标位置
- 高亮效果持续2秒

**测试场景**:
1. **摘要跳转**
   - 点击任意摘要卡片
   - 验证：左侧滚动到对应对话
   - 验证：对应消息高亮显示

2. **高亮效果**
   - 点击摘要后
   - 验证：消息有蓝色高亮边框
   - 等待2秒
   - 验证：高亮效果消失

## 🎯 Epic 3: 智能学习辅助

### US3.1 - 对话记忆
**用户故事**: 作为学习者，我希望AI能记住我们的对话历史，以便提供连贯的学习体验。

**验收标准**:
- AI回复基于历史对话上下文
- 能够引用之前的讨论内容
- 保持话题连贯性
- 避免重复回答相同问题

**测试场景**:
1. **上下文连贯性**
   - 问："什么是useState？"
   - AI回答后，继续问："它和类组件的state有什么区别？"
   - 验证：AI回复中提到useState相关内容

### US3.2 - 基于材料回答
**用户故事**: 作为学习者，我希望AI能基于上传的材料回答问题，以便深入理解内容。

**验收标准**:
- AI回复引用上传的文档内容
- 能够回答文档相关的具体问题
- 提供准确的信息引用
- 结合文档和通用知识回答

**测试场景**:
1. **文档相关问答**
   - 上传React官方文档
   - 问："文档中提到的Hook规则是什么？"
   - 验证：AI回复包含文档中的具体内容

## 📊 测试执行清单

### 功能测试
- [ ] 主题创建流程
- [ ] 文件上传功能
- [ ] 文本粘贴功能
- [ ] 主题列表展示
- [ ] 搜索和过滤
- [ ] 对话发送接收
- [ ] 摘要生成显示
- [ ] 摘要跳转功能
- [ ] 消息高亮效果

### 用户体验测试
- [ ] 界面响应速度
- [ ] 动画效果流畅性
- [ ] 错误提示友好性
- [ ] 加载状态清晰性
- [ ] 移动端适配

### 兼容性测试
- [ ] Chrome浏览器
- [ ] Firefox浏览器
- [ ] Safari浏览器
- [ ] 移动端浏览器

### 性能测试
- [ ] 页面加载时间
- [ ] 大文件上传性能
- [ ] 长对话列表滚动性能
- [ ] 内存使用情况

## 🐛 已知问题和限制

### 当前限制
1. **Mock数据**: 使用模拟数据，无真实后端
2. **文件处理**: 仅支持.txt和.md格式
3. **AI回复**: 使用预设回复模板
4. **搜索功能**: 仅支持简单文本匹配

### 待优化项
1. **性能优化**: 大量消息时的渲染性能
2. **错误处理**: 更完善的错误边界处理
3. **离线支持**: 添加离线缓存功能
4. **无障碍访问**: 改进键盘导航和屏幕阅读器支持

---

**测试环境**: 
- Node.js 18+
- 现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）
- 屏幕分辨率: 1920x1080 (桌面), 375x667 (移动)

**测试数据**: 
- 使用内置Mock数据
- 测试文件: demo/samples/ 目录下的示例文件
