# 部署指南

## 📋 概述

本文档提供了"知深学习导师"MVP前端演示应用的详细部署指南，包括开发环境、测试环境和生产环境的部署方案。

## 🛠️ 环境要求

### 基础要求
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **Git**: 最新版本

### 推荐配置
- **内存**: >= 4GB RAM
- **存储**: >= 2GB 可用空间
- **网络**: 稳定的互联网连接

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd master-know/demo/frontend-mvp
```

### 2. 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 启动开发服务器
```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

应用将在 `http://localhost:3001` 启动

## 🔧 开发环境配置

### 环境变量
创建 `.env.local` 文件（可选）：
```env
# API 基础URL（如果需要连接真实后端）
VITE_API_URL=http://localhost:8000

# 应用标题
VITE_APP_TITLE=知深学习导师 - MVP Demo

# 调试模式
VITE_DEBUG=true
```

### 开发工具配置

#### VS Code 推荐扩展
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### ESLint 配置
项目已包含 ESLint 配置，运行代码检查：
```bash
npm run lint
```

## 🏗️ 构建和部署

### 构建生产版本
```bash
# 构建
npm run build

# 预览构建结果
npm run preview
```

构建产物将生成在 `dist/` 目录中。

### 静态文件部署

#### 1. Nginx 部署
创建 Nginx 配置文件：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理 React Router
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip 压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

#### 2. Apache 部署
创建 `.htaccess` 文件：
```apache
Options -MultiViews
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ index.html [QSA,L]

# 启用 Gzip 压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### 云平台部署

#### 1. Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

或者通过 GitHub 集成自动部署。

#### 2. Netlify 部署
创建 `netlify.toml` 文件：
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"
```

#### 3. GitHub Pages 部署
```bash
# 安装 gh-pages
npm install --save-dev gh-pages

# 添加部署脚本到 package.json
"scripts": {
  "deploy": "gh-pages -d dist"
}

# 构建并部署
npm run build
npm run deploy
```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3001:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
```

### 构建和运行
```bash
# 构建镜像
docker build -t frontend-mvp .

# 运行容器
docker run -p 3001:80 frontend-mvp

# 或使用 docker-compose
docker-compose up -d
```

## 🔍 监控和日志

### 性能监控
建议集成以下监控工具：
- **Google Analytics**: 用户行为分析
- **Sentry**: 错误监控和性能追踪
- **Lighthouse CI**: 性能持续集成

### 日志配置
在生产环境中，建议配置结构化日志：
```javascript
// 在 main.tsx 中添加
if (import.meta.env.PROD) {
  // 禁用 console.log
  console.log = () => {};
  
  // 错误上报
  window.addEventListener('error', (event) => {
    // 发送错误到监控服务
  });
}
```

## 🔒 安全配置

### 内容安全策略 (CSP)
在 `index.html` 中添加：
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  font-src 'self' data:;
  connect-src 'self' https://api.your-domain.com;
">
```

### HTTPS 配置
生产环境必须使用 HTTPS：
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
}
```

## 🧪 测试部署

### 自动化测试
```bash
# 运行所有测试
npm test

# 运行 E2E 测试（如果配置了）
npm run test:e2e

# 运行性能测试
npm run test:performance
```

### 部署验证清单
- [ ] 应用正常启动
- [ ] 所有页面可访问
- [ ] 静态资源加载正常
- [ ] API 请求正常（如果有后端）
- [ ] 响应式设计正常
- [ ] 性能指标达标
- [ ] 错误处理正常
- [ ] 浏览器兼容性测试

## 🔄 CI/CD 配置

### GitHub Actions 示例
```yaml
name: Deploy Frontend

on:
  push:
    branches: [ main ]
    paths: [ 'demo/frontend-mvp/**' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: demo/frontend-mvp/package-lock.json
    
    - name: Install dependencies
      run: |
        cd demo/frontend-mvp
        npm ci
    
    - name: Run tests
      run: |
        cd demo/frontend-mvp
        npm test
    
    - name: Build
      run: |
        cd demo/frontend-mvp
        npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        working-directory: demo/frontend-mvp
```

## 📞 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
npm run clean
rm -rf node_modules package-lock.json
npm install

# 检查 Node.js 版本
node --version
```

#### 2. 路由不工作
确保服务器配置了 SPA 路由回退到 `index.html`。

#### 3. 静态资源 404
检查 `vite.config.ts` 中的 `base` 配置是否正确。

#### 4. 内存不足
```bash
# 增加 Node.js 内存限制
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

### 性能优化
- 启用 Gzip/Brotli 压缩
- 配置 CDN
- 启用浏览器缓存
- 使用 HTTP/2
- 优化图片资源

---

**维护者**: 开发团队  
**更新时间**: 2024-01-15  
**版本**: 1.0.0
